<view class="content data-v-5767d540"><u-form vue-id="d4d95874-1" model="{{form}}" error-type="{{['toast']}}" data-ref="uForm" class="data-v-5767d540 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><u-form-item vue-id="{{('d4d95874-2')+','+('d4d95874-1')}}" label="手机号:" labelWidth="160" prop="phone" class="data-v-5767d540" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('d4d95874-3')+','+('d4d95874-2')}}" placeholder="请输入手机号" value="{{form.phone}}" data-event-opts="{{[['^input',[['__set_model',['$0','phone','$event',[]],['form']]]]]}}" class="data-v-5767d540" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('d4d95874-4')+','+('d4d95874-1')}}" label="验证码:" labelWidth="160" prop="code" class="data-v-5767d540" bind:__l="__l" vue-slots="{{['default','right']}}"><u-input bind:input="__e" vue-id="{{('d4d95874-5')+','+('d4d95874-4')}}" placeholder="请输入验证码" value="{{form.code}}" data-event-opts="{{[['^input',[['__set_model',['$0','code','$event',[]],['form']]]]]}}" class="data-v-5767d540" bind:__l="__l"></u-input><u-verification-code vue-id="{{('d4d95874-6')+','+('d4d95874-4')}}" seconds="{{seconds}}" data-ref="uCode" data-event-opts="{{[['^change',[['codeChange']]]]}}" bind:change="__e" class="data-v-5767d540 vue-ref" bind:__l="__l"></u-verification-code><u-button vue-id="{{('d4d95874-7')+','+('d4d95874-4')}}" slot="right" type="primary" size="mini" data-event-opts="{{[['^click',[['getCode']]]]}}" bind:click="__e" class="data-v-5767d540" bind:__l="__l" vue-slots="{{['default']}}">{{tips}}</u-button></u-form-item><u-form-item vue-id="{{('d4d95874-8')+','+('d4d95874-1')}}" label="密码:" labelWidth="160" prop="password" class="data-v-5767d540" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('d4d95874-9')+','+('d4d95874-8')}}" placeholder="请输入要重置的密码" value="{{form.password}}" data-event-opts="{{[['^input',[['__set_model',['$0','password','$event',[]],['form']]]]]}}" class="data-v-5767d540" bind:__l="__l"></u-input></u-form-item><u-button vue-id="{{('d4d95874-10')+','+('d4d95874-1')}}" type="primary" data-event-opts="{{[['^click',[['submit']]]]}}" bind:click="__e" class="data-v-5767d540" bind:__l="__l" vue-slots="{{['default']}}">确定</u-button></u-form></view>