{"version": 3, "sources": ["webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-mask/u-mask.vue?b614", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-mask/u-mask.vue?b6e5", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-mask/u-mask.vue?2438", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-mask/u-mask.vue?b1ba", "uni-app:///uni_modules/uview-ui/components/u-mask/u-mask.vue", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-mask/u-mask.vue?f962", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-mask/u-mask.vue?cf52"], "names": ["name", "props", "show", "type", "default", "zIndex", "customStyle", "zoom", "duration", "maskClickAble", "data", "zoomStyle", "transform", "scale", "watch", "computed", "maskStyle", "style", "methods", "click"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxBA;AAAA;AAAA;AAAA;AAAgmB,CAAgB,0nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACUpnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA,gBAaA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;QACA;MACA;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;EACA;EACAM;IACA;MACAC;QACAC;MACA;MACAC;IACA;EACA;EACAC;IACAZ;MACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;EACA;EACAa;IACAC;MACA;MACAC;MACA,mFACAA;MACAA;MACA;MACA,kFACAA,QACA,iBACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;ACnGA;AAAA;AAAA;AAAA;AAAmqC,CAAgB,yoCAAG,EAAC,C;;;;;;;;;;;ACAvrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-mask/u-mask.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-mask.vue?vue&type=template&id=9c19d32e&scoped=true&\"\nvar renderjs\nimport script from \"./u-mask.vue?vue&type=script&lang=js&\"\nexport * from \"./u-mask.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-mask.vue?vue&type=style&index=0&id=9c19d32e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"9c19d32e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-mask/u-mask.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-mask.vue?vue&type=template&id=9c19d32e&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.maskStyle, _vm.zoomStyle])\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      $event.stopPropagation()\n      $event.preventDefault()\n      return (function () {})($event)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-mask.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-mask.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"u-mask\" hover-stop-propagation :style=\"[maskStyle, zoomStyle]\" @tap=\"click\" @touchmove.stop.prevent=\"() => {}\" :class=\"{\n\t\t'u-mask-zoom': zoom,\n\t\t'u-mask-show': show\n\t}\">\n\t\t<slot />\n\t</view>\n</template>\n\n<script>\n\t/**\n\t * mask 遮罩\n\t * @description 创建一个遮罩层，用于强调特定的页面元素，并阻止用户对遮罩下层的内容进行操作，一般用于弹窗场景\n\t * @tutorial https://www.uviewui.com/components/mask.html\n\t * @property {Boolean} show 是否显示遮罩（默认false）\n\t * @property {String Number} z-index z-index 层级（默认1070）\n\t * @property {Object} custom-style 自定义样式对象，见上方说明\n\t * @property {String Number} duration 动画时长，单位毫秒（默认300）\n\t * @property {Boolean} zoom 是否使用scale对遮罩进行缩放（默认true）\n\t * @property {Boolean} mask-click-able 遮罩是否可点击，为false时点击不会发送click事件（默认true）\n\t * @event {Function} click mask-click-able为true时，点击遮罩发送此事件\n\t * @example <u-mask :show=\"show\" @click=\"show = false\"></u-mask>\n\t */\n\texport default {\n\t\tname: \"u-mask\",\n\t\tprops: {\n\t\t\t// 是否显示遮罩\n\t\t\tshow: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: false\n\t\t\t},\n\t\t\t// 层级z-index\n\t\t\tzIndex: {\n\t\t\t\ttype: [Number, String],\n\t\t\t\tdefault: ''\n\t\t\t},\n\t\t\t// 用户自定义样式\n\t\t\tcustomStyle: {\n\t\t\t\ttype: Object,\n\t\t\t\tdefault () {\n\t\t\t\t\treturn {}\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 遮罩的动画样式， 是否使用使用zoom进行scale进行缩放\n\t\t\tzoom: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: true\n\t\t\t},\n\t\t\t// 遮罩的过渡时间，单位为ms\n\t\t\tduration: {\n\t\t\t\ttype: [Number, String],\n\t\t\t\tdefault: 300\n\t\t\t},\n\t\t\t// 是否可以通过点击遮罩进行关闭\n\t\t\tmaskClickAble: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: true\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tzoomStyle: {\n\t\t\t\t\ttransform: ''\n\t\t\t\t},\n\t\t\t\tscale: 'scale(1.2, 1.2)'\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\tshow(n) {\n\t\t\t\tif(n && this.zoom) {\n\t\t\t\t\t// 当展示遮罩的时候，设置scale为1，达到缩小(原来为1.2)的效果\n\t\t\t\t\tthis.zoomStyle.transform = 'scale(1, 1)';\n\t\t\t\t} else if(!n && this.zoom) {\n\t\t\t\t\t// 当隐藏遮罩的时候，设置scale为1.2，达到放大(因为显示遮罩时已重置为1)的效果\n\t\t\t\t\tthis.zoomStyle.transform = this.scale;\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\tmaskStyle() {\n\t\t\t\tlet style = {};\n\t\t\t\tstyle.backgroundColor = \"rgba(0, 0, 0, 0.6)\";\n\t\t\t\tif(this.show) style.zIndex = this.zIndex ? this.zIndex : this.$u.zIndex.mask;\n\t\t\t\telse style.zIndex = -1;\n\t\t\t\tstyle.transition = `all ${this.duration / 1000}s ease-in-out`;\n\t\t\t\t// 判断用户传递的对象是否为空，不为空就进行合并\n\t\t\t\tif (Object.keys(this.customStyle).length) style = { \n\t\t\t\t\t...style,\n\t\t\t\t\t...this.customStyle\n\t\t\t\t};\n\t\t\t\treturn style;\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\tclick() {\n\t\t\t\tif (!this.maskClickAble) return;\n\t\t\t\tthis.$emit('click');\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/style.components.scss\";\n\t\n\t.u-mask {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\topacity: 0;\n\t\ttransition: transform 0.3s;\n\t}\n\n\t.u-mask-show {\n\t\topacity: 1;\n\t}\n\t\n\t.u-mask-zoom {\n\t\ttransform: scale(1.2, 1.2);\n\t}\n</style>\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-mask.vue?vue&type=style&index=0&id=9c19d32e&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-mask.vue?vue&type=style&index=0&id=9c19d32e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753690709265\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}