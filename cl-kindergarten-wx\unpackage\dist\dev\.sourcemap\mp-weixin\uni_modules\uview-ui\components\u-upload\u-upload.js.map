{"version": 3, "sources": ["webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-upload/u-upload.vue?1fa4", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-upload/u-upload.vue?5cf1", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-upload/u-upload.vue?96c7", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-upload/u-upload.vue?282a", "uni-app:///uni_modules/uview-ui/components/u-upload/u-upload.vue", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-upload/u-upload.vue?1358", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-upload/u-upload.vue?e4a7"], "names": ["name", "props", "showUploadList", "type", "default", "action", "maxCount", "showProgress", "disabled", "imageMode", "header", "formData", "sizeType", "sourceType", "previewFullImage", "multiple", "deletable", "maxSize", "fileList", "uploadText", "autoUpload", "showTips", "customBtn", "width", "height", "delBgColor", "delColor", "delIcon", "to<PERSON><PERSON>", "beforeUpload", "beforeRemove", "limitType", "index", "mounted", "data", "lists", "isInCount", "uploading", "watch", "immediate", "handler", "val", "url", "error", "progress", "methods", "clear", "reUpload", "selectFile", "camera", "compressed", "maxDuration", "chooseFile", "uni", "count", "success", "fail", "then", "res", "file", "catch", "showToast", "title", "icon", "upload", "retry", "uploadFile", "beforeResponse", "task", "filePath", "complete", "uploadError", "deleteItem", "content", "handlerDeleteItem", "remove", "doPreviewImage", "urls", "current", "checkFileExt", "fileExt", "noArrowExt"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2OAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7EA;AAAA;AAAA;AAAA;AAAkmB,CAAgB,4nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACqDtnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAtCA,gBAuCA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;QACA;MACA;IACA;IACA;IACAO;MACAR;MACAC;QACA;MACA;IACA;IACA;IACAJ;MACAG;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;QACA;MACA;IACA;IACAS;MACAV;MACAC;QACA;MACA;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;IACA;IACAa;MACAd;MACAC;IACA;IACA;IACAc;MACAf;MACAC;QACA;MACA;IACA;IACA;IACAe;MACAhB;MACAC;IACA;IACA;IACAgB;MACAjB;MACAC;IACA;IACA;IACAiB;MACAlB;MACAC;IACA;IACA;IACAkB;MACAnB;MACAC;IACA;IACA;IACAmB;MACApB;MACAC;IACA;IACA;IACAoB;MACArB;MACAC;IACA;IACA;IACAqB;MACAtB;MACAC;IACA;IACA;IACAsB;MACAvB;MACAC;IACA;IACA;IACAuB;MACAxB;MACAC;IACA;IACA;IACAwB;MACAzB;MACAC;IACA;IACA;IACAyB;MACA1B;MACAC;IACA;IACA;IACA0B;MACA3B;MACAC;IACA;IACA;IACA2B;MACA5B;MACAC;QACA;QACA;QACA;MACA;IACA;IACA;IACA4B;MACA7B;MACAC;IACA;EACA;EACA6B;EACAC;IACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACApB;MACAqB;MACAC;QAAA;QACAC;UACA;UACA;UACA;UACA;YACA;UACA;UACA;UACA;YAAAC;YAAAC;YAAAC;UAAA;QACA;MACA;IACA;IACA;IACAT;MACA;IACA;EACA;EACAU;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;QAAAhD;QAAAM;QAAAS;QAAAE;QAAAL;QAAAuB;QAAAc;QAAAC;QAAAC;QAAAtC;MACA;MACA;MACA;MACAuC;QACAC;UACAC;UACAzC;UACAD;UACA2C;UACAC;QACA;MACA;MACAJ,WACAK;QACA;QACA;QACAC;UACA;UACA;;UAEA;UACA;UACA;YACA;YACA;UACA;YACA;cACA;cACA;cACA;YACA;YACAvB;cACAO;cACAE;cACAD;cACAgB;YACA;UACA;QACA;QACA;QACA;QACA;MACA,GACAC;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACAR;UACAS;UACAC;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACAZ;QACAS;MACA;MACA;IACA;IACA;IACAI;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAlC;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA,MAIA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA,MAIA;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACAmC,gGACA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACAA;kBACA;gBAAA,CACA;kBACA;kBACA;gBACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBAAA,iCAEA;cAAA;gBAAA,IAMA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAGA;gBACA;gBACA;gBACAC;kBACA1B;kBACA2B;kBACArE;kBACAW;kBACAD;kBAIA6C;oBACA;oBACA;oBACA;sBACA;oBACA;sBACA;sBACA;sBACA;sBACA;sBACA;oBACA;kBACA;kBACAC;oBACA;kBACA;kBACAc;oBACAjB;oBACA;oBACA;oBACA;kBACA;gBACA;gBACAe;kBACA;oBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAG;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MAAA;MACAnB;QACAS;QACAW;QACAlB;UAAA;YAAA;YAAA;cAAA;gBAAA;kBAAA;oBAAA,KACAG;sBAAA;sBAAA;oBAAA;oBAAA,MAGA;sBAAA;sBAAA;oBAAA;oBACA;oBACAS,gGACA;oBAAA,MACA;sBAAA;sBAAA;oBAAA;oBAAA;oBAAA,OACAA;sBACA;sBACA;oBACA;sBACA;sBACA;oBACA;kBAAA;oBAAA;oBAAA;kBAAA;oBACA;sBACA;sBACA;oBACA;sBACA;sBACA;oBACA;kBAAA;oBAAA;oBAAA;kBAAA;oBAEA;oBACA;kBAAA;kBAAA;oBAAA;gBAAA;cAAA;YAAA;UAAA,CAGA;UAAA;YAAA;UAAA;UAAA;QAAA;MACA;IACA;IACA;IACAO;MACA;MACA;QACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;QAAA;MAAA;MACAvB;QACAwB;QACAC;QACAvB;UACA;QACA;QACAC;UACAH;YACAS;YACAC;UACA;QACA;MACA;IACA;IACA;IACAgB;MACA;MACA;MACA;MACA;MACA;MACA;;MAIA;;MAEAC;;MAEA;MACAC;QACA;QACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;ACjjBA;AAAA;AAAA;AAAA;AAAqqC,CAAgB,2oCAAG,EAAC,C;;;;;;;;;;;ACAzrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-upload/u-upload.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-upload.vue?vue&type=template&id=69e2a36e&scoped=true&\"\nvar renderjs\nimport script from \"./u-upload.vue?vue&type=script&lang=js&\"\nexport * from \"./u-upload.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-upload.vue?vue&type=style&index=0&id=69e2a36e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"69e2a36e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-upload/u-upload.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-upload.vue?vue&type=template&id=69e2a36e&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uLineProgress: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-line-progress/u-line-progress\" */ \"@/uni_modules/uview-ui/components/u-line-progress/u-line-progress.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = !_vm.disabled\n    ? _vm.__map(_vm.lists, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var g0 = _vm.showUploadList ? _vm.$u.addUnit(_vm.width) : null\n        var g1 = _vm.showUploadList ? _vm.$u.addUnit(_vm.height) : null\n        return {\n          $orig: $orig,\n          g0: g0,\n          g1: g1,\n        }\n      })\n    : null\n  _vm.$initSSP()\n  var g2 = !_vm.disabled ? _vm.lists.length : null\n  var g3 =\n    !_vm.disabled && _vm.maxCount > g2 && !_vm.customBtn\n      ? _vm.$u.addUnit(_vm.width)\n      : null\n  var g4 =\n    !_vm.disabled && _vm.maxCount > g2 && !_vm.customBtn\n      ? _vm.$u.addUnit(_vm.height)\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n      },\n    }\n  )\n  if (_vm.$scope.data.scopedSlotsCompiler === \"augmented\") {\n    _vm.$setSSP(\"file\", {\n      file: _vm.lists,\n    })\n  }\n  _vm.$callSSP()\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-upload.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-upload.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"u-upload\" v-if=\"!disabled\">\n\t\t<view\n\t\t\tv-if=\"showUploadList\"\n\t\t\tclass=\"u-list-item u-preview-wrap\"\n\t\t\tv-for=\"(item, index) in lists\"\n\t\t\t:key=\"index\"\n\t\t\t:style=\"{\n\t\t\t\twidth: $u.addUnit(width),\n\t\t\t\theight: $u.addUnit(height)\n\t\t\t}\"\n\t\t>\n\t\t\t<view\n\t\t\t\tv-if=\"deletable\"\n\t\t\t\tclass=\"u-delete-icon\"\n\t\t\t\**********=\"deleteItem(index)\"\n\t\t\t\t:style=\"{\n\t\t\t\t\tbackground: delBgColor\n\t\t\t\t}\"\n\t\t\t>\n\t\t\t\t<u-icon class=\"u-icon\" :name=\"delIcon\" size=\"20\" :color=\"delColor\"></u-icon>\n\t\t\t</view>\n\t\t\t<u-line-progress\n\t\t\t\tv-if=\"showProgress && item.progress > 0 && item.progress != 100 && !item.error\"\n\t\t\t\t:show-percent=\"false\"\n\t\t\t\theight=\"16\"\n\t\t\t\tclass=\"u-progress\"\n\t\t\t\t:percent=\"item.progress\"\n\t\t\t></u-line-progress>\n\t\t\t<view @tap.stop=\"retry(index)\" v-if=\"item.error\" class=\"u-error-btn\">点击重试</view>\n\t\t\t<image @tap.stop=\"doPreviewImage(item.url || item.path, index)\" class=\"u-preview-image\" v-if=\"!item.isImage\" :src=\"item.url || item.path\" :mode=\"imageMode\"></image>\n\t\t</view>\n\t\t<slot name=\"file\" :file=\"lists\"></slot>\n\t\t<view style=\"display: inline-block;\" @tap=\"selectFile\" v-if=\"maxCount > lists.length\">\n\t\t\t<slot name=\"addBtn\"></slot>\n\t\t\t<view\n\t\t\t\tv-if=\"!customBtn\"\n\t\t\t\tclass=\"u-list-item u-add-wrap\"\n\t\t\t\thover-class=\"u-add-wrap__hover\"\n\t\t\t\thover-stay-time=\"150\"\n\t\t\t\t:style=\"{\n\t\t\t\t\twidth: $u.addUnit(width),\n\t\t\t\t\theight: $u.addUnit(height)\n\t\t\t\t}\"\n\t\t\t>\n\t\t\t\t<u-icon name=\"plus\" class=\"u-add-btn\" size=\"40\"></u-icon>\n\t\t\t\t<view class=\"u-add-tips\">{{ uploadText }}</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n/**\n * upload 图片上传\n * @description 该组件用于上传图片场景\n * @tutorial https://www.uviewui.com/components/upload.html\n * @property {String} action 服务器上传地址\n * @property {String Number} max-count 最大选择图片的数量（默认99）\n * @property {Boolean} custom-btn 如果需要自定义选择图片的按钮，设置为true（默认false）\n * @property {Boolean} show-progress 是否显示进度条（默认true）\n * @property {Boolean} disabled 是否启用(显示/移仓)组件（默认false）\n * @property {String} image-mode 预览图片等显示模式，可选值为uni的image的mode属性值（默认aspectFill）\n * @property {String} del-icon 右上角删除图标名称，只能为uView内置图标\n * @property {String} del-bg-color 右上角关闭按钮的背景颜色\n * @property {String | Number} index 在各个回调事件中的最后一个参数返回，用于区别是哪一个组件的事件\n * @property {String} del-color 右上角关闭按钮图标的颜色\n * @property {Object} header 上传携带的头信息，对象形式\n * @property {Object} form-data 上传额外携带的参数\n * @property {String} name 上传文件的字段名，供后端获取使用（默认file）\n * @property {Array<String>} size-type original 原图，compressed 压缩图，默认二者都有（默认['original', 'compressed']）\n * @property {Array<String>} source-type 选择图片的来源，album-从相册选图，camera-使用相机，默认二者都有（默认['album', 'camera']）\n * @property {Boolean} preview-full-image\t是否可以通过uni.previewImage预览已选择的图片（默认true）\n * @property {Boolean} multiple\t是否开启图片多选，部分安卓机型不支持（默认true）\n * @property {Boolean} deletable 是否显示删除图片的按钮（默认true）\n * @property {String Number} max-size 选择单个文件的最大大小，单位B(byte)，默认不限制（默认Number.MAX_VALUE）\n * @property {Array<Object>} file-list 默认显示的图片列表，数组元素为对象，必须提供url属性\n * @property {Boolean} upload-text 选择图片按钮的提示文字（默认“选择图片”）\n * @property {Boolean} auto-upload 选择完图片是否自动上传，见上方说明（默认true）\n * @property {Boolean} show-tips 特殊情况下是否自动提示toast，见上方说明（默认true）\n * @property {Boolean} show-upload-list 是否显示组件内部的图片预览（默认true）\n * @event {Function} on-oversize 图片大小超出最大允许大小\n * @event {Function} on-preview 全屏预览图片时触发\n * @event {Function} on-remove 移除图片时触发\n * @event {Function} on-success 图片上传成功时触发\n * @event {Function} on-change 图片上传后，无论成功或者失败都会触发\n * @event {Function} on-error 图片上传失败时触发\n * @event {Function} on-progress 图片上传过程中的进度变化过程触发\n * @event {Function} on-uploaded 所有图片上传完毕触发\n * @event {Function} on-choose-complete 每次选择图片后触发，只是让外部可以得知每次选择后，内部的文件列表\n * @example <u-upload :action=\"action\" :file-list=\"fileList\" ></u-upload>\n */\nexport default {\n\tname: 'u-upload',\n\tprops: {\n\t\t//是否显示组件自带的图片预览功能\n\t\tshowUploadList: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t},\n\t\t// 后端地址\n\t\taction: {\n\t\t\ttype: String,\n\t\t\tdefault: ''\n\t\t},\n\t\t// 最大上传数量\n\t\tmaxCount: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: 52\n\t\t},\n\t\t//  是否显示进度条\n\t\tshowProgress: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t},\n\t\t// 是否启用\n\t\tdisabled: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t},\n\t\t// 预览上传的图片时的裁剪模式，和image组件mode属性一致\n\t\timageMode: {\n\t\t\ttype: String,\n\t\t\tdefault: 'aspectFill'\n\t\t},\n\t\t// 头部信息\n\t\theader: {\n\t\t\ttype: Object,\n\t\t\tdefault() {\n\t\t\t\treturn {};\n\t\t\t}\n\t\t},\n\t\t// 额外携带的参数\n\t\tformData: {\n\t\t\ttype: Object,\n\t\t\tdefault() {\n\t\t\t\treturn {};\n\t\t\t}\n\t\t},\n\t\t// 上传的文件字段名\n\t\tname: {\n\t\t\ttype: String,\n\t\t\tdefault: 'file'\n\t\t},\n\t\t// 所选的图片的尺寸, 可选值为original compressed\n\t\tsizeType: {\n\t\t\ttype: Array,\n\t\t\tdefault() {\n\t\t\t\treturn ['original', 'compressed'];\n\t\t\t}\n\t\t},\n\t\tsourceType: {\n\t\t\ttype: Array,\n\t\t\tdefault() {\n\t\t\t\treturn ['album', 'camera'];\n\t\t\t}\n\t\t},\n\t\t// 是否在点击预览图后展示全屏图片预览\n\t\tpreviewFullImage: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t},\n\t\t// 是否开启图片多选，部分安卓机型不支持\n\t\tmultiple: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t},\n\t\t// 是否展示删除按钮\n\t\tdeletable: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t},\n\t\t// 文件大小限制，单位为byte\n\t\tmaxSize: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: Number.MAX_VALUE\n\t\t},\n\t\t// 显示已上传的文件列表\n\t\tfileList: {\n\t\t\ttype: Array,\n\t\t\tdefault() {\n\t\t\t\treturn [];\n\t\t\t}\n\t\t},\n\t\t// 上传区域的提示文字\n\t\tuploadText: {\n\t\t\ttype: String,\n\t\t\tdefault: '选择图片'\n\t\t},\n\t\t// 是否自动上传\n\t\tautoUpload: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t},\n\t\t// 是否显示toast消息提示\n\t\tshowTips: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t},\n\t\t// 是否通过slot自定义传入选择图标的按钮\n\t\tcustomBtn: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t},\n\t\t// 内部预览图片区域和选择图片按钮的区域宽度\n\t\twidth: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: 200\n\t\t},\n\t\t// 内部预览图片区域和选择图片按钮的区域高度\n\t\theight: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: 200\n\t\t},\n\t\t// 右上角关闭按钮的背景颜色\n\t\tdelBgColor: {\n\t\t\ttype: String,\n\t\t\tdefault: '#fa3534'\n\t\t},\n\t\t// 右上角关闭按钮的叉号图标的颜色\n\t\tdelColor: {\n\t\t\ttype: String,\n\t\t\tdefault: '#ffffff'\n\t\t},\n\t\t// 右上角删除图标名称，只能为uView内置图标\n\t\tdelIcon: {\n\t\t\ttype: String,\n\t\t\tdefault: 'close'\n\t\t},\n\t\t// 如果上传后的返回值为json字符串，是否自动转json\n\t\ttoJson: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t},\n\t\t// 上传前的钩子，每个文件上传前都会执行\n\t\tbeforeUpload: {\n\t\t\ttype: Function,\n\t\t\tdefault: null\n\t\t},\n\t\t// 移除文件前的钩子\n\t\tbeforeRemove: {\n\t\t\ttype: Function,\n\t\t\tdefault: null\n\t\t},\n\t\t// 允许上传的图片后缀\n\t\tlimitType:{\n\t\t\ttype: Array,\n\t\t\tdefault() {\n\t\t\t\t// 支付宝小程序真机选择图片的后缀为\"image\"\n\t\t\t\t// https://opendocs.alipay.com/mini/api/media-image\n\t\t\t\treturn ['png', 'jpg', 'jpeg', 'webp', 'gif', 'image'];\n\t\t\t}\n\t\t},\n\t\t// 在各个回调事件中的最后一个参数返回，用于区别是哪一个组件的事件\n\t\tindex: {\n\t\t\ttype: [Number, String],\n\t\t\tdefault: ''\n\t\t}\n\t},\n\tmounted() {},\n\tdata() {\n\t\treturn {\n\t\t\tlists: [],\n\t\t\tisInCount: true,\n\t\t\tuploading: false\n\t\t};\n\t},\n\twatch: {\n\t\tfileList: {\n\t\t\timmediate: true,\n\t\t\thandler(val) {\n\t\t\t\tval.map(value => {\n\t\t\t\t\t// 首先检查内部是否已经添加过这张图片，因为外部绑定了一个对象给fileList的话(对象引用)，进行修改外部fileList\n\t\t\t\t\t// 时，会触发watch，导致重新把原来的图片再次添加到this.lists\n\t\t\t\t\t// 数组的some方法意思是，只要数组元素有任意一个元素条件符合，就返回true，而另一个数组的every方法的意思是数组所有元素都符合条件才返回true\n\t\t\t\t\tlet tmp = this.lists.some(val => {\n\t\t\t\t\t\treturn val.url == value.url;\n\t\t\t\t\t})\n\t\t\t\t\t// 如果内部没有这个图片(tmp为false)，则添加到内部\n\t\t\t\t\t!tmp && this.lists.push({ url: value.url, error: false, progress: 100 });\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t// 监听lists的变化，发出事件\n\t\tlists(n) {\n\t\t\tthis.$emit('on-list-change', n, this.index);\n\t\t}\n\t},\n\tmethods: {\n\t\t// 清除列表\n\t\tclear() {\n\t\t\tthis.lists = [];\n\t\t},\n\t\t// 重新上传队列中上传失败的所有文件\n\t\treUpload() {\n\t\t\tthis.uploadFile();\n\t\t},\n\t\t// 选择图片\n\t\tselectFile() {\n\t\t\tif (this.disabled) return;\n\t\t\tconst { name = '', maxCount, multiple, maxSize, sizeType, lists, camera, compressed, maxDuration, sourceType } = this;\n\t\t\tlet chooseFile = null;\n\t\t\tconst newMaxCount = maxCount - lists.length;\n\t\t\t// 设置为只选择图片的时候使用 chooseImage 来实现\n\t\t\tchooseFile = new Promise((resolve, reject) => {\n\t\t\t\tuni.chooseImage({\n\t\t\t\t\tcount: multiple ? (newMaxCount > 9 ? 9 : newMaxCount) : 1,\n\t\t\t\t\tsourceType: sourceType,\n\t\t\t\t\tsizeType,\n\t\t\t\t\tsuccess: resolve,\n\t\t\t\t\tfail: reject\n\t\t\t\t});\n\t\t\t});\n\t\t\tchooseFile\n\t\t\t\t.then(res => {\n\t\t\t\t\tlet file = null;\n\t\t\t\t\tlet listOldLength = this.lists.length;\n\t\t\t\t\tres.tempFiles.map((val, index) => {\n\t\t\t\t\t\t// 检查文件后缀是否允许，如果不在this.limitType内，就会返回false\n\t\t\t\t\t\tif(!this.checkFileExt(val)) return ;\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 如果是非多选，index大于等于1或者超出最大限制数量时，不处理\n\t\t\t\t\t\tif (!multiple && index >= 1) return;\n\t\t\t\t\t\tif (val.size > maxSize) {\n\t\t\t\t\t\t\tthis.$emit('on-oversize', val, this.lists, this.index);\n\t\t\t\t\t\t\tthis.showToast('超出允许的文件大小');\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tif (maxCount <= lists.length) {\n\t\t\t\t\t\t\t\tthis.$emit('on-exceed', val, this.lists, this.index);\n\t\t\t\t\t\t\t\tthis.showToast('超出最大允许的文件个数');\n\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tlists.push({\n\t\t\t\t\t\t\t\turl: val.path,\n\t\t\t\t\t\t\t\tprogress: 0,\n\t\t\t\t\t\t\t\terror: false,\n\t\t\t\t\t\t\t\tfile: val\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\t// 每次图片选择完，抛出一个事件，并将当前内部选择的图片数组抛出去\n\t\t\t\t\tthis.$emit('on-choose-complete', this.lists, this.index);\n\t\t\t\t\tif (this.autoUpload) this.uploadFile(listOldLength);\n\t\t\t\t})\n\t\t\t\t.catch(error => {\n\t\t\t\t\tthis.$emit('on-choose-fail', error);\n\t\t\t\t});\n\t\t},\n\t\t// 提示用户消息\n\t\tshowToast(message, force = false) {\n\t\t\tif (this.showTips || force) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: message,\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t// 该方法供用户通过ref调用，手动上传\n\t\tupload() {\n\t\t\tthis.uploadFile();\n\t\t},\n\t\t// 对失败的图片重新上传\n\t\tretry(index) {\n\t\t\tthis.lists[index].progress = 0;\n\t\t\tthis.lists[index].error = false;\n\t\t\tthis.lists[index].response = null;\n\t\t\tuni.showLoading({\n\t\t\t\ttitle: '重新上传'\n\t\t\t});\n\t\t\tthis.uploadFile(index);\n\t\t},\n\t\t// 上传图片\n\t\tasync uploadFile(index = 0) {\n\t\t\tif (this.disabled) return;\n\t\t\tif (this.uploading) return;\n\t\t\t// 全部上传完成\n\t\t\tif (index >= this.lists.length) {\n\t\t\t\tthis.$emit('on-uploaded', this.lists, this.index);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t// 检查是否是已上传或者正在上传中\n\t\t\tif (this.lists[index].progress == 100) {\n\t\t\t\tif (this.autoUpload == false) this.uploadFile(index + 1);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t// 执行before-upload钩子\n\t\t\tif(this.beforeUpload && typeof(this.beforeUpload) === 'function') {\n\t\t\t\t// 执行回调，同时传入索引和文件列表当作参数\n\t\t\t\t// 在微信，支付宝等环境(H5正常)，会导致父组件定义的customBack()函数体中的this变成子组件的this\n\t\t\t\t// 通过bind()方法，绑定父组件的this，让this.customBack()的this为父组件的上下文\n\t\t\t\t// 因为upload组件可能会被嵌套在其他组件内，比如u-form，这时this.$parent其实为u-form的this，\n\t\t\t\t// 非页面的this，所以这里需要往上历遍，一直寻找到最顶端的$parent，这里用了this.$u.$parent.call(this)\n\t\t\t\t// 明白意思即可，无需纠结this.$u.$parent.call(this)的细节\n\t\t\t\tlet beforeResponse = this.beforeUpload.bind(this.$u.$parent.call(this))(index, this.lists);\n\t\t\t\t// 判断是否返回了promise\n\t\t\t\tif (!!beforeResponse && typeof beforeResponse.then === 'function') {\n\t\t\t\t\tawait beforeResponse.then(res => {\n\t\t\t\t\t\t// promise返回成功，不进行动作，继续上传\n\t\t\t\t\t}).catch(err => {\n\t\t\t\t\t\t// 进入catch回调的话，继续下一张\n\t\t\t\t\t\treturn this.uploadFile(index + 1);\n\t\t\t\t\t})\n\t\t\t\t} else if(beforeResponse === false) {\n\t\t\t\t\t// 如果返回false，继续下一张图片的上传\n\t\t\t\t\treturn this.uploadFile(index + 1);\n\t\t\t\t} else {\n\t\t\t\t\t// 此处为返回\"true\"的情形，这里不写代码，就跳过此处，继续执行当前的上传逻辑\n\t\t\t\t}\n\t\t\t}\n\t\t\t// 检查上传地址\n\t\t\tif (!this.action) {\n\t\t\t\tthis.showToast('请配置上传地址', true);\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tthis.lists[index].error = false;\n\t\t\tthis.uploading = true;\n\t\t\t// 创建上传对象\n\t\t\tconst task = uni.uploadFile({\n\t\t\t\turl: this.action,\n\t\t\t\tfilePath: this.lists[index].url,\n\t\t\t\tname: this.name,\n\t\t\t\tformData: this.formData,\n\t\t\t\theader: this.header,\n\t\t\t\t// #ifdef MP-ALIPAY\n\t\t\t\tfileType:'image',\n\t\t\t\t// #endif\n\t\t\t\tsuccess: res => {\n\t\t\t\t\t// 判断是否json字符串，将其转为json格式\n\t\t\t\t\tlet data = this.toJson && this.$u.test.jsonString(res.data) ? JSON.parse(res.data) : res.data;\n\t\t\t\t\tif (![200, 201, 204].includes(res.statusCode)) {\n\t\t\t\t\t\tthis.uploadError(index, data);\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 上传成功\n\t\t\t\t\t\tthis.lists[index].response = data;\n\t\t\t\t\t\tthis.lists[index].progress = 100;\n\t\t\t\t\t\tthis.lists[index].error = false;\n\t\t\t\t\t\tthis.$emit('on-success', data, index, this.lists, this.index);\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tfail: e => {\n\t\t\t\t\tthis.uploadError(index, e);\n\t\t\t\t},\n\t\t\t\tcomplete: res => {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tthis.uploading = false;\n\t\t\t\t\tthis.uploadFile(index + 1);\n\t\t\t\t\tthis.$emit('on-change', res, index, this.lists, this.index);\n\t\t\t\t}\n\t\t\t});\n\t\t\ttask.onProgressUpdate(res => {\n\t\t\t\tif (res.progress > 0) {\n\t\t\t\t\tthis.lists[index].progress = res.progress;\n\t\t\t\t\tthis.$emit('on-progress', res, index, this.lists, this.index);\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t// 上传失败\n\t\tuploadError(index, err) {\n\t\t\tthis.lists[index].progress = 0;\n\t\t\tthis.lists[index].error = true;\n\t\t\tthis.lists[index].response = null;\n\t\t\tthis.$emit('on-error', err, index, this.lists, this.index);\n\t\t\tthis.showToast('上传失败，请重试');\n\t\t},\n\t\t// 删除一个图片\n\t\tdeleteItem(index) {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '提示',\n\t\t\t\tcontent: '您确定要删除此项吗？',\n\t\t\t\tsuccess: async (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t// 先检查是否有定义before-remove移除前钩子\n\t\t\t\t\t\t// 执行before-remove钩子\n\t\t\t\t\t\tif(this.beforeRemove && typeof(this.beforeRemove) === 'function') {\n\t\t\t\t\t\t\t// 此处钩子执行 原理同before-remove参数，见上方注释\n\t\t\t\t\t\t\tlet beforeResponse = this.beforeRemove.bind(this.$u.$parent.call(this))(index, this.lists);\n\t\t\t\t\t\t\t// 判断是否返回了promise\n\t\t\t\t\t\t\tif (!!beforeResponse && typeof beforeResponse.then === 'function') {\n\t\t\t\t\t\t\t\tawait beforeResponse.then(res => {\n\t\t\t\t\t\t\t\t\t// promise返回成功，不进行动作，继续上传\n\t\t\t\t\t\t\t\t\tthis.handlerDeleteItem(index);\n\t\t\t\t\t\t\t\t}).catch(err => {\n\t\t\t\t\t\t\t\t\t// 如果进入promise的reject，终止删除操作\n\t\t\t\t\t\t\t\t\tthis.showToast('已终止移除');\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t} else if(beforeResponse === false) {\n\t\t\t\t\t\t\t\t// 返回false，终止删除\n\t\t\t\t\t\t\t\tthis.showToast('已终止移除');\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t// 如果返回true，执行删除操作\n\t\t\t\t\t\t\t\tthis.handlerDeleteItem(index);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// 如果不存在before-remove钩子，\n\t\t\t\t\t\t\tthis.handlerDeleteItem(index);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t// 执行移除图片的动作，上方代码只是判断是否可以移除\n\t\thandlerDeleteItem(index) {\n\t\t\t// 如果文件正在上传中，终止上传任务，进度在0 < progress < 100则意味着正在上传\n\t\t\tif (this.lists[index].progress < 100 && this.lists[index].progress > 0) {\n\t\t\t\ttypeof this.lists[index].uploadTask != 'undefined' && this.lists[index].uploadTask.abort();\n\t\t\t}\n\t\t\tthis.lists.splice(index, 1);\n\t\t\tthis.$forceUpdate();\n\t\t\tthis.$emit('on-remove', index, this.lists, this.index);\n\t\t\tthis.showToast('移除成功');\n\t\t},\n\t\t// 用户通过ref手动的形式，移除一张图片\n\t\tremove(index) {\n\t\t\t// 判断索引的合法范围\n\t\t\tif (index >= 0 && index < this.lists.length) {\n\t\t\t\tthis.lists.splice(index, 1);\n\t\t\t\tthis.$emit('on-list-change', this.lists, this.index);\n\t\t\t}\n\t\t},\n\t\t// 预览图片\n\t\tdoPreviewImage(url, index) {\n\t\t\tif (!this.previewFullImage) return;\n\t\t\tconst images = this.lists.map(item => item.url || item.path);\n\t\t\tuni.previewImage({\n\t\t\t\turls: images,\n\t\t\t\tcurrent: url,\n\t\t\t\tsuccess: () => {\n\t\t\t\t\tthis.$emit('on-preview', url, this.lists, this.index);\n\t\t\t\t},\n\t\t\t\tfail: () => {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '预览图片失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t// 判断文件后缀是否允许\n\t\tcheckFileExt(file) {\n\t\t\t// 检查是否在允许的后缀中\n\t\t\tlet noArrowExt = false;\n\t\t\t// 获取后缀名\n\t\t\tlet fileExt = '';\n\t\t\tconst reg = /.+\\./;\n\t\t\t// 如果是H5，需要从name中判断\n\t\t\t// #ifdef H5\n\t\t\tfileExt = file.name.replace(reg, \"\").toLowerCase();\n\t\t\t// #endif\n\t\t\t// 非H5，需要从path中读取后缀\n\t\t\t// #ifndef H5\n\t\t\tfileExt = file.path.replace(reg, \"\").toLowerCase();\n\t\t\t// #endif\n\t\t\t// 使用数组的some方法，只要符合limitType中的一个，就返回true\n\t\t\tnoArrowExt = this.limitType.some(ext => {\n\t\t\t\t// 转为小写\n\t\t\t\treturn ext.toLowerCase() === fileExt;\n\t\t\t})\n\t\t\tif(!noArrowExt) this.showToast(`不允许选择${fileExt}格式的文件`);\n\t\t\treturn noArrowExt;\n\t\t}\n\t}\n};\n</script>\n\n<style lang=\"scss\" scoped>\n@import '../../libs/css/style.components.scss';\n\n.u-upload {\n\t@include vue-flex;\n\tflex-wrap: wrap;\n\talign-items: center;\n}\n\n.u-list-item {\n\twidth: 200rpx;\n\theight: 200rpx;\n\toverflow: hidden;\n\tmargin: 10rpx;\n\tbackground: rgb(244, 245, 246);\n\tposition: relative;\n\tborder-radius: 10rpx;\n\t/* #ifndef APP-NVUE */\n\tdisplay: flex;\n\t/* #endif */\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.u-preview-wrap {\n\tborder: 1px solid rgb(235, 236, 238);\n}\n\n.u-add-wrap {\n\tflex-direction: column;\n\tcolor: $u-content-color;\n\tfont-size: 26rpx;\n}\n\n.u-add-tips {\n\tmargin-top: 20rpx;\n\tline-height: 40rpx;\n}\n\n.u-add-wrap__hover {\n\tbackground-color: rgb(235, 236, 238);\n}\n\n.u-preview-image {\n\tdisplay: block;\n\twidth: 100%;\n\theight: 100%;\n\tborder-radius: 10rpx;\n}\n\n.u-delete-icon {\n\tposition: absolute;\n\ttop: 10rpx;\n\tright: 10rpx;\n\tz-index: 10;\n\tbackground-color: $u-type-error;\n\tborder-radius: 100rpx;\n\twidth: 44rpx;\n\theight: 44rpx;\n\t@include vue-flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.u-icon {\n\t@include vue-flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.u-progress {\n\tposition: absolute;\n\tbottom: 10rpx;\n\tleft: 8rpx;\n\tright: 8rpx;\n\tz-index: 9;\n\twidth: auto;\n}\n\n.u-error-btn {\n\tcolor: #ffffff;\n\tbackground-color: $u-type-error;\n\tfont-size: 20rpx;\n\tpadding: 4px 0;\n\ttext-align: center;\n\tposition: absolute;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\tz-index: 9;\n\tline-height: 1;\n}\n</style>\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-upload.vue?vue&type=style&index=0&id=69e2a36e&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-upload.vue?vue&type=style&index=0&id=69e2a36e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753690217271\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}