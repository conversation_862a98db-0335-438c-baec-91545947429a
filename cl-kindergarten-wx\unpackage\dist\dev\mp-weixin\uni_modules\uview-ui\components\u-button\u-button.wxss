@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.u-btn.data-v-2bf0e569::after {
  border: none;
}
.u-btn.data-v-2bf0e569 {
  position: relative;
  border: 0;
  display: inline-flex;
  overflow: visible;
  line-height: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 0 40rpx;
  z-index: 1;
  box-sizing: border-box;
  transition: all 0.15s;
}
.u-btn--bold-border.data-v-2bf0e569 {
  border: 1px solid #ffffff;
}
.u-btn--default.data-v-2bf0e569 {
  color: #606266;
  border-color: #c0c4cc;
  background-color: #ffffff;
}
.u-btn--primary.data-v-2bf0e569 {
  color: #ffffff;
  border-color: #2979ff;
  background-color: #2979ff;
}
.u-btn--success.data-v-2bf0e569 {
  color: #ffffff;
  border-color: #19be6b;
  background-color: #19be6b;
}
.u-btn--error.data-v-2bf0e569 {
  color: #ffffff;
  border-color: #fa3534;
  background-color: #fa3534;
}
.u-btn--warning.data-v-2bf0e569 {
  color: #ffffff;
  border-color: #ff9900;
  background-color: #ff9900;
}
.u-btn--default--disabled.data-v-2bf0e569 {
  color: #ffffff;
  border-color: #e4e7ed;
  background-color: #ffffff;
}
.u-btn--primary--disabled.data-v-2bf0e569 {
  color: #ffffff !important;
  border-color: #a0cfff !important;
  background-color: #a0cfff !important;
}
.u-btn--success--disabled.data-v-2bf0e569 {
  color: #ffffff !important;
  border-color: #71d5a1 !important;
  background-color: #71d5a1 !important;
}
.u-btn--error--disabled.data-v-2bf0e569 {
  color: #ffffff !important;
  border-color: #fab6b6 !important;
  background-color: #fab6b6 !important;
}
.u-btn--warning--disabled.data-v-2bf0e569 {
  color: #ffffff !important;
  border-color: #fcbd71 !important;
  background-color: #fcbd71 !important;
}
.u-btn--primary--plain.data-v-2bf0e569 {
  color: #2979ff !important;
  border-color: #a0cfff !important;
  background-color: #ecf5ff !important;
}
.u-btn--success--plain.data-v-2bf0e569 {
  color: #19be6b !important;
  border-color: #71d5a1 !important;
  background-color: #dbf1e1 !important;
}
.u-btn--error--plain.data-v-2bf0e569 {
  color: #fa3534 !important;
  border-color: #fab6b6 !important;
  background-color: #fef0f0 !important;
}
.u-btn--warning--plain.data-v-2bf0e569 {
  color: #ff9900 !important;
  border-color: #fcbd71 !important;
  background-color: #fdf6ec !important;
}
.u-hairline-border.data-v-2bf0e569:after {
  content: " ";
  position: absolute;
  pointer-events: none;
  box-sizing: border-box;
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  left: 0;
  top: 0;
  width: 199.8%;
  height: 199.7%;
  -webkit-transform: scale(0.5, 0.5);
  transform: scale(0.5, 0.5);
  border: 1px solid currentColor;
  z-index: 1;
}
.u-wave-ripple.data-v-2bf0e569 {
  z-index: 0;
  position: absolute;
  border-radius: 100%;
  background-clip: padding-box;
  pointer-events: none;
  -webkit-user-select: none;
          user-select: none;
  -webkit-transform: scale(0);
          transform: scale(0);
  opacity: 1;
  -webkit-transform-origin: center;
          transform-origin: center;
}
.u-wave-ripple.u-wave-active.data-v-2bf0e569 {
  opacity: 0;
  -webkit-transform: scale(2);
          transform: scale(2);
  transition: opacity 1s linear, -webkit-transform 0.4s linear;
  transition: opacity 1s linear, transform 0.4s linear;
  transition: opacity 1s linear, transform 0.4s linear, -webkit-transform 0.4s linear;
}
.u-round-circle.data-v-2bf0e569 {
  border-radius: 100rpx;
}
.u-round-circle.data-v-2bf0e569::after {
  border-radius: 100rpx;
}
.u-loading.data-v-2bf0e569::after {
  background-color: rgba(255, 255, 255, 0.35);
}
.u-size-default.data-v-2bf0e569 {
  font-size: 30rpx;
  height: 80rpx;
  line-height: 80rpx;
}
.u-size-medium.data-v-2bf0e569 {
  display: inline-flex;
  width: auto;
  font-size: 26rpx;
  height: 70rpx;
  line-height: 70rpx;
  padding: 0 80rpx;
}
.u-size-mini.data-v-2bf0e569 {
  display: inline-flex;
  width: auto;
  font-size: 22rpx;
  padding-top: 1px;
  height: 50rpx;
  line-height: 50rpx;
  padding: 0 20rpx;
}
.u-primary-plain-hover.data-v-2bf0e569 {
  color: #ffffff !important;
  background: #2b85e4 !important;
}
.u-default-plain-hover.data-v-2bf0e569 {
  color: #2b85e4 !important;
  background: #ecf5ff !important;
}
.u-success-plain-hover.data-v-2bf0e569 {
  color: #ffffff !important;
  background: #18b566 !important;
}
.u-warning-plain-hover.data-v-2bf0e569 {
  color: #ffffff !important;
  background: #f29100 !important;
}
.u-error-plain-hover.data-v-2bf0e569 {
  color: #ffffff !important;
  background: #dd6161 !important;
}
.u-info-plain-hover.data-v-2bf0e569 {
  color: #ffffff !important;
  background: #82848a !important;
}
.u-default-hover.data-v-2bf0e569 {
  color: #2b85e4 !important;
  border-color: #2b85e4 !important;
  background-color: #ecf5ff !important;
}
.u-primary-hover.data-v-2bf0e569 {
  background: #2b85e4 !important;
  color: #fff;
}
.u-success-hover.data-v-2bf0e569 {
  background: #18b566 !important;
  color: #fff;
}
.u-info-hover.data-v-2bf0e569 {
  background: #82848a !important;
  color: #fff;
}
.u-warning-hover.data-v-2bf0e569 {
  background: #f29100 !important;
  color: #fff;
}
.u-error-hover.data-v-2bf0e569 {
  background: #dd6161 !important;
  color: #fff;
}
