<view data-event-opts="{{[['tap',[['onClick',['$event']]]]]}}" class="u-image data-v-042b391e" style="{{$root.s0}}" bindtap="__e"><block wx:if="{{!isError}}"><image class="u-image__image data-v-042b391e" style="{{'border-radius:'+(shape=='circle'?'50%':$root.g0)+';'}}" src="{{src}}" mode="{{mode}}" lazy-load="{{lazyLoad}}" show-menu-by-longpress="{{showMenuByLongpress}}" data-event-opts="{{[['error',[['onErrorHandler',['$event']]]],['load',[['onLoadHandler',['$event']]]]]}}" binderror="__e" bindload="__e"></image></block><block wx:if="{{showLoading&&loading}}"><view class="u-image__loading data-v-042b391e" style="{{'border-radius:'+(shape=='circle'?'50%':$root.g1)+';'+('background-color:'+(bgColor)+';')}}"><block wx:if="{{$slots.loading}}"><slot name="loading"></slot></block><block wx:else><u-icon vue-id="813ae896-1" name="{{loadingIcon}}" width="{{width}}" height="{{height}}" class="data-v-042b391e" bind:__l="__l"></u-icon></block></view></block><block wx:if="{{showError&&isError&&!loading}}"><view class="u-image__error data-v-042b391e" style="{{'border-radius:'+(shape=='circle'?'50%':$root.g2)+';'}}"><block wx:if="{{$slots.error}}"><slot name="error"></slot></block><block wx:else><u-icon vue-id="813ae896-2" name="{{errorIcon}}" width="{{width}}" height="{{height}}" class="data-v-042b391e" bind:__l="__l"></u-icon></block></view></block></view>