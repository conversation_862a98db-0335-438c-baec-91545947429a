<view data-event-opts="{{[['tap',[['inputClick',['$event']]]]]}}" class="{{['u-input','data-v-113bc24f',(border)?'u-input--border':'',(validateState)?'u-input--error':'']}}" style="{{'padding:'+('0 '+(border?20:0)+'rpx')+';'+('border-color:'+(borderColor)+';')+('text-align:'+(inputAlign)+';')}}" catchtap="__e"><block wx:if="{{type=='textarea'}}"><textarea class="u-input__input u-input__textarea data-v-113bc24f" style="{{$root.s0}}" placeholder="{{placeholder}}" placeholderStyle="{{placeholderStyle}}" disabled="{{disabled}}" maxlength="{{inputMaxlength}}" fixed="{{fixed}}" focus="{{focus}}" autoHeight="{{autoHeight}}" selection-end="{{uSelectionEnd}}" selection-start="{{uSelectionStart}}" cursor-spacing="{{getCursorSpacing}}" show-confirm-bar="{{showConfirmbar}}" adjust-position="{{adjustPosition}}" data-event-opts="{{[['input',[['handleInput',['$event']]]],['blur',[['handleBlur',['$event']]]],['focus',[['onFocus',['$event']]]],['confirm',[['onConfirm',['$event']]]]]}}" value="{{defaultValue}}" bindinput="__e" bindblur="__e" bindfocus="__e" bindconfirm="__e"></textarea></block><block wx:else><input class="u-input__input data-v-113bc24f" style="{{$root.s1}}" type="{{type=='password'?'text':type}}" password="{{type=='password'&&!showPassword}}" placeholder="{{placeholder}}" placeholderStyle="{{placeholderStyle}}" disabled="{{disabled||type==='select'}}" maxlength="{{inputMaxlength}}" focus="{{focus}}" confirmType="{{confirmType}}" cursor-spacing="{{getCursorSpacing}}" selection-end="{{uSelectionEnd}}" selection-start="{{uSelectionStart}}" show-confirm-bar="{{showConfirmbar}}" adjust-position="{{adjustPosition}}" data-event-opts="{{[['focus',[['onFocus',['$event']]]],['blur',[['handleBlur',['$event']]]],['input',[['handleInput',['$event']]]],['confirm',[['onConfirm',['$event']]]]]}}" value="{{defaultValue}}" bindfocus="__e" bindblur="__e" bindinput="__e" bindconfirm="__e"/></block><view class="u-input__right-icon u-flex data-v-113bc24f"><block wx:if="{{clearable&&value!=''&&focused}}"><view data-event-opts="{{[['tap',[['onClear',['$event']]]]]}}" class="u-input__right-icon__clear u-input__right-icon__item data-v-113bc24f" bindtap="__e"><u-icon vue-id="163d0897-1" size="32" name="close-circle-fill" color="#c0c4cc" class="data-v-113bc24f" bind:__l="__l"></u-icon></view></block><block wx:if="{{passwordIcon&&type=='password'}}"><view class="u-input__right-icon__clear u-input__right-icon__item data-v-113bc24f"><u-icon vue-id="163d0897-2" size="32" name="{{!showPassword?'eye':'eye-fill'}}" color="#c0c4cc" data-event-opts="{{[['^click',[['e0']]]]}}" bind:click="__e" class="data-v-113bc24f" bind:__l="__l"></u-icon></view></block><block wx:if="{{type=='select'}}"><view class="{{['u-input__right-icon--select','u-input__right-icon__item','data-v-113bc24f',(selectOpen)?'u-input__right-icon--select--reverse':'']}}"><u-icon vue-id="163d0897-3" name="arrow-down-fill" size="26" color="#c0c4cc" class="data-v-113bc24f" bind:__l="__l"></u-icon></view></block></view></view>