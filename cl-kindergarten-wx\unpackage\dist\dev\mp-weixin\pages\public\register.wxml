<view class="content"><u-form class="vue-ref" vue-id="41ebb566-1" model="{{form}}" error-type="{{['toast']}}" data-ref="uForm" bind:__l="__l" vue-slots="{{['default']}}"><u-form-item vue-id="{{('41ebb566-2')+','+('41ebb566-1')}}" label="登陆帐号:" prop="username" label-width="170" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('41ebb566-3')+','+('41ebb566-2')}}" placeholder="请输入登陆帐号" value="{{form.username}}" data-event-opts="{{[['^input',[['__set_model',['$0','username','$event',[]],['form']]]]]}}" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('41ebb566-4')+','+('41ebb566-1')}}" label="真实姓名:" label-width="170" prop="name" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('41ebb566-5')+','+('41ebb566-4')}}" placeholder="请输入真实姓名" value="{{form.name}}" data-event-opts="{{[['^input',[['__set_model',['$0','name','$event',[]],['form']]]]]}}" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('41ebb566-6')+','+('41ebb566-1')}}" label="手机号:" label-width="170" prop="phone" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('41ebb566-7')+','+('41ebb566-6')}}" placeholder="请输入输入手机号码" value="{{form.phone}}" data-event-opts="{{[['^input',[['__set_model',['$0','phone','$event',[]],['form']]]]]}}" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('41ebb566-8')+','+('41ebb566-1')}}" label="登陆密码:" label-width="170" prop="password" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('41ebb566-9')+','+('41ebb566-8')}}" type="password" placeholder="请输入登陆密码" value="{{form.password}}" data-event-opts="{{[['^input',[['__set_model',['$0','password','$event',[]],['form']]]]]}}" bind:__l="__l"></u-input></u-form-item><view class="agreement"><u-checkbox vue-id="{{('41ebb566-10')+','+('41ebb566-1')}}" size="40rpx" value="{{check}}" data-event-opts="{{[['^change',[['checkboxChange']]],['^input',[['__set_model',['','check','$event',[]]]]]]}}" bind:change="__e" bind:input="__e" bind:__l="__l"></u-checkbox><view class="agreement-text">我已知晓并同意<text data-event-opts="{{[['tap',[['showPopup',[3]]]]]}}" style="color:cornflowerblue;" bindtap="__e">《用户服务协议》</text>及<text data-event-opts="{{[['tap',[['showPopup',[4]]]]]}}" style="color:cornflowerblue;" bindtap="__e">《隐私政策》</text></view></view><view class="submit_con"><u-button vue-id="{{('41ebb566-11')+','+('41ebb566-1')}}" type="primary" data-event-opts="{{[['^click',[['submit']]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}">提交注册</u-button></view></u-form><u-popup bind:input="__e" vue-id="41ebb566-12" mode="bottom" length="60%" closeable="{{true}}" value="{{popupVisible}}" data-event-opts="{{[['^input',[['__set_model',['','popupVisible','$event',[]]]]]]}}" bind:__l="__l" vue-slots="{{['default']}}"><scroll-view class="popup-content" scroll-y="{{true}}"><rich-text style="line-height:50rpx;" nodes="{{protocolsContent}}"></rich-text></scroll-view></u-popup></view>