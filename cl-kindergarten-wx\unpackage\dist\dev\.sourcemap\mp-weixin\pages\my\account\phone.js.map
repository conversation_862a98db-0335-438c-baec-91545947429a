{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/pages/my/account/phone.vue?9fde", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/pages/my/account/phone.vue?fd4b", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/pages/my/account/phone.vue?a9c8", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/pages/my/account/phone.vue?61ad", "uni-app:///pages/my/account/phone.vue", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/pages/my/account/phone.vue?1a74", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/pages/my/account/phone.vue?78c4"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "baseInfo", "phone", "form", "new_phone", "code", "rules", "required", "message", "trigger", "validator", "min", "type", "seconds", "refCode", "tips", "onReady", "onLoad", "methods", "submit", "setTimeout", "uni", "console", "getUserInfo", "codeChange", "getCode", "title"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2OAEN;AACP,KAAK;AACL;AACA,aAAa,+TAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,yTAEN;AACP,KAAK;AACL;AACA,aAAa,uPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrDA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACqBnnB;;;;;;;;;;;;;;;;;;;;;eACA;EACAC;IAAA;IACA;MACAC;QACAC;MACA;MACAC;QACAC;QACAC;MACA;MACAC;QACAF,YACA;UACAG;UACAC;UACAC;QACA,GACA;UACAC;YACA;UACA;UACAF;UACAC;QACA,EACA;QACAJ,OACA;UACAE;UACAI;UACAC;UACAJ;UACAC;QACA;MAEA;MACAI;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACA;UACA;UACA;YACA;cACA;cACAC;gBACAC;cACA;YACA;cACA;YACA;UACA;QACA;UACAC;QACA;MACA;IACA;IACAC;MAAA;MACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;QACA;QACAJ;UACAK;QACA;QACAN;UACAC;UACA;UACA;UACA;UACA;QACA;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClHA;AAAA;AAAA;AAAA;AAAkqC,CAAgB,woCAAG,EAAC,C;;;;;;;;;;;ACAtrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/my/account/phone.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/my/account/phone.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./phone.vue?vue&type=template&id=08d10be2&scoped=true&\"\nvar renderjs\nimport script from \"./phone.vue?vue&type=script&lang=js&\"\nexport * from \"./phone.vue?vue&type=script&lang=js&\"\nimport style0 from \"./phone.vue?vue&type=style&index=0&id=08d10be2&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"08d10be2\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/my/account/phone.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./phone.vue?vue&type=template&id=08d10be2&scoped=true&\"", "var components\ntry {\n  components = {\n    uForm: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-form/u-form\" */ \"@/uni_modules/uview-ui/components/u-form/u-form.vue\"\n      )\n    },\n    uFormItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-form-item/u-form-item\" */ \"@/uni_modules/uview-ui/components/u-form-item/u-form-item.vue\"\n      )\n    },\n    uInput: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-input/u-input\" */ \"@/uni_modules/uview-ui/components/u-input/u-input.vue\"\n      )\n    },\n    uVerificationCode: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-verification-code/u-verification-code\" */ \"@/uni_modules/uview-ui/components/u-verification-code/u-verification-code.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-button/u-button\" */ \"@/uni_modules/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./phone.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./phone.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"content\">\r\n\t\t<u-form :model=\"form\" ref=\"uForm\" :error-type=\"['toast']\">\r\n\t\t\t<u-form-item label=\"原手机号:\" labelWidth=\"160\">{{baseInfo.phone}}</u-form-item>\r\n\t\t\t\r\n\t\t\t<u-form-item label=\"新手机号:\" labelWidth=\"160\"  prop=\"new_phone\">\r\n\t\t\t\t<u-input placeholder=\"请输入手机号\" v-model=\"form.new_phone\" />\r\n\t\t\t</u-form-item>\r\n\t\t\t\r\n\t\t\t<u-form-item label=\"验证码:\" labelWidth=\"160\"  prop=\"code\">\r\n\t\t\t\t<u-input placeholder=\"请输入验证码\" v-model=\"form.code\" />\r\n\t\t\t\t<u-verification-code :seconds=\"seconds\"  ref=\"uCode\" \r\n\t\t\t\t\t\t@change=\"codeChange\"></u-verification-code>\r\n\t\t\t\t<u-button slot=\"right\" type=\"primary\" size=\"mini\" @click=\"getCode\">{{tips}}</u-button>\r\n\t\t\t</u-form-item>\r\n\t\t\t<u-button type=\"primary\" @click=\"submit\">确定</u-button>\r\n\t\t</u-form>\r\n\t</view>\n\t\n</template>\n<script>\r\nimport {toast, useRouter} from '@/utils/utils.js'\t\nexport default {\n\tdata() {\n\t\treturn {\r\n\t\t\tbaseInfo: {\r\n\t\t\t  phone : '',\r\n\t\t\t},\r\n\t\t\tform: {\r\n\t\t\t\tnew_phone: '',\r\n\t\t\t\tcode: ''\r\n\t\t\t},\r\n\t\t\trules: {\r\n\t\t\t\tnew_phone: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\tmessage: '请输入手机号',\r\n\t\t\t\t\t\ttrigger: 'blur,change'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tvalidator: (rule, value, callback) => {\r\n\t\t\t\t\t\t\treturn this.$u.test.mobile(value);\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tmessage: '手机号码不正确',\r\n\t\t\t\t\t\ttrigger: ['change','blur'],\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\tcode: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\tmin: 4,\r\n\t\t\t\t\t\ttype: 'number',\r\n\t\t\t\t\t\tmessage: '验证码格式错误',\r\n\t\t\t\t\t\ttrigger: 'change'\r\n\t\t\t\t\t}\r\n\t\t\t\t]\r\n\t\t\t},\r\n\t\t\tseconds: 60,\r\n\t\t\trefCode: null,\r\n\t\t\ttips:'获取验证码'\n\t\t};\n\t},\r\n\tonReady() {\r\n\t\t\tthis.$refs.uForm.setRules(this.rules);\r\n\t},\r\n\tonLoad(){\r\n\t\tthis.getUserInfo()\r\n\t},\r\n\tmethods: {\r\n\t\tsubmit(){\r\n\t\t\tthis.$refs.uForm.validate(valid => {\r\n\t\t\t\tif (valid) {\r\n\t\t\t\t\t//验证通过，执行TODO\r\n\t\t\t\t\tthis.$api.phoneBind(this.form).then(res => {\r\n\t\t\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\t\t\ttoast(res.msg)\r\n\t\t\t\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t\t\t\t},500) \r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\ttoast(res.msg)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}) \r\n\t\t\t\t} else {\r\n\t\t\t\t\tconsole.log('验证失败');\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tgetUserInfo() {\r\n\t\t\tthis.$api.baseInfo().then(res => {\r\n\t\t\t\tthis.baseInfo = res.data\r\n\t\t\t})\r\n\t\t},\r\n\t\tcodeChange(text) {\r\n\t\t\tthis.tips = text;\r\n\t\t},\r\n\t\tgetCode() {\r\n\t\t\tif(this.$refs.uCode.canGetCode) {\r\n\t\t\t\t// 模拟向后端请求验证码\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '正在获取验证码'\r\n\t\t\t\t})\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t// 这里此提示会被this.start()方法中的提示覆盖\r\n\t\t\t\t\tthis.$u.toast('验证码已发送');\r\n\t\t\t\t\t// 通知验证码组件内部开始倒计时\r\n\t\t\t\t\tthis.$refs.uCode.start();\r\n\t\t\t\t}, 2000);\r\n\t\t\t} else {\r\n\t\t\t\tthis.$u.toast('倒计时结束后再发送');\r\n\t\t\t}\r\n\t\t}\r\n\t}\n};\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.content{padding:35rpx}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./phone.vue?vue&type=style&index=0&id=08d10be2&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./phone.vue?vue&type=style&index=0&id=08d10be2&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753690216445\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}