@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
page.data-v-598eabfd {
  min-height: 100%;
}
.banner.data-v-598eabfd {
  height: 380rpx;
  position: relative;
  background-color: #ccc;
  flex-direction: row;
  overflow: hidden;
}
.banner-img.data-v-598eabfd {
  width: 100%;
}
.title-area.data-v-598eabfd {
  position: absolute;
  left: 15rpx;
  right: 15rpx;
  bottom: 15rpx;
  z-index: 11;
}
.title-text.data-v-598eabfd {
  font-size: 40rpx;
  font-weight: 400;
  line-height: 20rpx;
  lines: 2;
  color: #ffffff;
  overflow: hidden;
}
.article-meta.data-v-598eabfd {
  padding: 10rpx 15rpx;
  font-size: 30rpx;
}
.article-meta-text.data-v-598eabfd {
  color: gray;
  font-size: 30rpx;
}
.article-text.data-v-598eabfd {
  font-size: 32rpx;
  line-height: 25rpx;
  margin: 0 10rpx;
}
.article-author.data-v-598eabfd {
  font-size: 32rpx;
}
.article-time.data-v-598eabfd {
  font-size: 30rpx;
}
.article-content.data-v-598eabfd {
  font-size: 36rpx;
  padding: 0 35rpx;
  margin-bottom: 35rpx;
  overflow: hidden;
  color: #333;
}
.article-content ._img.data-v-598eabfd {
  max-width: 100%;
}
.article-content ._p.data-v-598eabfd {
  line-height: 40rpx;
  margin-top: 40rpx;
}
