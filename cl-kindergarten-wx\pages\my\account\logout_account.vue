<template>
	<view class="content">
		<view class="u-content">
			<u-parse :html="content"></u-parse>	
		</view>
	
		
		<view class="tipsArea">
			<view class="operation">
			<u-checkbox v-model="agree">我同意&lt;&lt;注销协议&gt;&gt;，提交后帐号将不再恢复</u-checkbox>
			</view>
			<view class="btn">
				<u-button type="primary" style="width: 98%;margin-bottom: 15rpx;" @click="confirme">注销帐号</u-button>
				
			</view>
		</view>
		
		<u-modal v-model="showModal" showCancelButton title="注销" content='确定要注销帐号吗?' @cancel="closeShowModal" @confirm="confirmOperation"></u-modal>
	</view>
</template>
<script>
import {toast, clearStorageSync, setStorageSync, getStorageSync, useRouter} from '@/utils/utils.js'
export default {
	data() {
		return {
			content: '',
			show:true,
			agree : false,
			showModal: false
		}
	},
	onLoad(op) {
		//this.id  = op.id
		this.getPage()
	},
	onShow() {
	},
	onReady() {
	},
	methods: {
		
		getPage(){
			const param={
				id:6
			}
			this.$api.page(param).then(res => {
				if (res.code ==1 ) {
					this.content = res.data.content
				} 
			})
		},
		confirme(){
			if(!this.agree) return toast('请查看并同意相关协议');
			this.showModal = true
		},
		confirmOperation() {
			this.$api.logout_account().then(res => {
				if (res.code ==0 ) {
					toast(res.msg);
				} 
			})
			
		},
		closeShowModal(){
			this.showModal = false
		}
	}
}
</script>
<style lang="scss">	
	.u-content{
		padding: 20rpx;
		color: $u-content-color;
		font-size: 28rpx;
		line-height: 1.8;	
		padding-bottom: 250rpx;
	}
	.tipsArea{position: fixed; bottom: 0;background: #fdf6ec;width: 100%;}
	.operation{padding:20rpx;padding-top: 20rpx;
		
	}
</style>
