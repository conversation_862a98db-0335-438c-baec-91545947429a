<view class="content data-v-e1ef5678"><view class="data-v-e1ef5678"><u-cell-group vue-id="5202c4c2-1" class="data-v-e1ef5678" bind:__l="__l" vue-slots="{{['default']}}"><u-cell-item vue-id="{{('5202c4c2-2')+','+('5202c4c2-1')}}" title="通知" arrow="{{false}}" class="data-v-e1ef5678" bind:__l="__l" vue-slots="{{['default']}}"><u-switch bind:change="__e" bind:input="__e" vue-id="{{('5202c4c2-3')+','+('5202c4c2-2')}}" value="{{costs_display}}" data-event-opts="{{[['^change',[['costs_display_change']]],['^input',[['__set_model',['','costs_display','$event',[]]]]]]}}" class="data-v-e1ef5678" bind:__l="__l"></u-switch></u-cell-item></u-cell-group></view></view>