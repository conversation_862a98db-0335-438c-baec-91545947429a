@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.con_box {
  display: flex;
  flex-direction: nowarp;
  height: 240rpx;
  margin: 20rpx;
  border-bottom: 1rpx solid #efefef;
}
.con_box .photo {
  width: 240rpx;
  height: 150rpx;
}
.con_box .photo image {
  width: 240rpx;
  height: 150rpx;
}
.con_box .text_con {
  flex: 1;
  margin-left: 30rpx;
}
.con_box .text_con .title {
  font-weight: bold;
  font-size: 28rpx;
}
.con_box .text_con .addtime {
  color: #e3e3e3;
  line-height: 60rpx;
}
.con_box .text_con .desc {
  color: #e9e9e9;
  overflow: hidden;
  text-overflow: ellipsis;
  /* 超出部分省略号 */
  display: -webkit-box;
  /** 对象作为伸缩盒子模型显示 **/
  -webkit-box-orient: vertical;
  /** 设置或检索伸缩盒对象的子元素的排列方式 **/
  -webkit-line-clamp: 2;
  /** 显示的行数 **/
}
