<view class="content data-v-76ed59e0"><u-form vue-id="66bce014-1" model="{{form}}" error-type="{{['toast']}}" data-ref="uForm" class="data-v-76ed59e0 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><u-form-item vue-id="{{('66bce014-2')+','+('66bce014-1')}}" label="原邮箱:" labelWidth="160" class="data-v-76ed59e0" bind:__l="__l" vue-slots="{{['default']}}">{{baseInfo.email}}</u-form-item><u-form-item vue-id="{{('66bce014-3')+','+('66bce014-1')}}" label="新邮箱:" labelWidth="160" prop="new_email" class="data-v-76ed59e0" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('66bce014-4')+','+('66bce014-3')}}" placeholder="请输入新邮箱" value="{{form.new_email}}" data-event-opts="{{[['^input',[['__set_model',['$0','new_email','$event',[]],['form']]]]]}}" class="data-v-76ed59e0" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('66bce014-5')+','+('66bce014-1')}}" label="验证码:" labelWidth="160" prop="code" class="data-v-76ed59e0" bind:__l="__l" vue-slots="{{['default','right']}}"><u-input bind:input="__e" vue-id="{{('66bce014-6')+','+('66bce014-5')}}" placeholder="请输入验证码" value="{{form.code}}" data-event-opts="{{[['^input',[['__set_model',['$0','code','$event',[]],['form']]]]]}}" class="data-v-76ed59e0" bind:__l="__l"></u-input><u-verification-code vue-id="{{('66bce014-7')+','+('66bce014-5')}}" seconds="{{seconds}}" data-ref="uCode" data-event-opts="{{[['^change',[['codeChange']]]]}}" bind:change="__e" class="data-v-76ed59e0 vue-ref" bind:__l="__l"></u-verification-code><u-button vue-id="{{('66bce014-8')+','+('66bce014-5')}}" slot="right" type="primary" size="mini" data-event-opts="{{[['^click',[['getCode']]]]}}" bind:click="__e" class="data-v-76ed59e0" bind:__l="__l" vue-slots="{{['default']}}">{{tips}}</u-button></u-form-item><u-button vue-id="{{('66bce014-9')+','+('66bce014-1')}}" type="primary" data-event-opts="{{[['^click',[['submit']]]]}}" bind:click="__e" class="data-v-76ed59e0" bind:__l="__l" vue-slots="{{['default']}}">确定</u-button></u-form></view>