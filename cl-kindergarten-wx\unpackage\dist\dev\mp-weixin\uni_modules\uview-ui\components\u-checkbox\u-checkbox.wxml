<view class="u-checkbox data-v-c4a74aee" style="{{$root.s0}}"><view data-event-opts="{{[['tap',[['toggle',['$event']]]]]}}" class="{{['u-checkbox__icon-wrap','data-v-c4a74aee',iconClass]}}" style="{{$root.s1}}" bindtap="__e"><u-icon class="u-checkbox__icon-wrap__icon data-v-c4a74aee" vue-id="17df779d-1" name="checkbox-mark" size="{{checkboxIconSize}}" color="{{iconColor}}" bind:__l="__l"></u-icon></view><view data-event-opts="{{[['tap',[['onClickLabel',['$event']]]]]}}" class="u-checkbox__label data-v-c4a74aee" style="{{'font-size:'+($root.g0)+';'}}" bindtap="__e"><slot></slot></view></view>