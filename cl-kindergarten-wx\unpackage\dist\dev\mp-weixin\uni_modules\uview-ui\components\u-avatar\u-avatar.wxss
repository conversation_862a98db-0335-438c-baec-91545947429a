@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.u-avatar.data-v-d3651d6e {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #606266;
  border-radius: 10px;
  position: relative;
}
.u-avatar__img.data-v-d3651d6e {
  width: 100%;
  height: 100%;
}
.u-avatar__sex.data-v-d3651d6e {
  position: absolute;
  width: 32rpx;
  color: #ffffff;
  height: 32rpx;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  border-radius: 100rpx;
  top: 5%;
  z-index: 1;
  right: -7%;
  border: 1px #ffffff solid;
}
.u-avatar__sex--man.data-v-d3651d6e {
  background-color: #2979ff;
}
.u-avatar__sex--woman.data-v-d3651d6e {
  background-color: #fa3534;
}
.u-avatar__sex--none.data-v-d3651d6e {
  background-color: #ff9900;
}
.u-avatar__level.data-v-d3651d6e {
  position: absolute;
  width: 32rpx;
  color: #ffffff;
  height: 32rpx;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  border-radius: 100rpx;
  bottom: 5%;
  z-index: 1;
  right: -7%;
  border: 1px #ffffff solid;
  background-color: #ff9900;
}
