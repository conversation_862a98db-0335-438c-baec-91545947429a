# 创联幼儿园核算系统 (移动端)

<div align="center">
  <img src="static/logo.png" alt="创联幼儿园核算系统" width="120" height="120">
  
  <h3>专为中小型幼儿园打造的智能财务核算与消息推送系统</h3>
  
  [![uni-app](https://img.shields.io/badge/uni--app-Vue.js-brightgreen.svg)](https://uniapp.dcloud.io/)
  [![uView](https://img.shields.io/badge/uView-2.x-blue.svg)](https://v1.uviewui.com/)
  [![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
</div>

## 📋 项目简介

创联幼儿园核算系统移动端是一款基于 uni-app 开发的跨平台应用，专门为中小型幼儿园提供便捷的财务核算和消息推送服务。系统通过移动端应用，让幼儿园管理者能够随时随地处理财务事务，提高工作效率。

### 🎯 核心价值

- **💰 财务核算数字化** - 告别传统手工记账，实现智能化财务管理
- **📱 移动办公便捷化** - 随时随地处理财务事务，不受时间地点限制  
- **📢 消息推送及时化** - 重要财务信息实时推送，确保信息传达
- **👥 多角色协同化** - 支持园长、财务、教师等多角色协同工作

## ✨ 主要功能

### 🏠 首页模块
- 财务数据概览
- 待办事项提醒
- 快捷操作入口
- 系统公告展示

### 📊 财务核算
- 收入支出记录
- 费用分类管理
- 财务报表查看
- 预算执行监控

### 👤 个人中心
- 个人信息管理
- 账号安全设置
- 密码修改
- 账号注销

### 🔐 用户认证
- 安全登录/注册
- 忘记密码找回
- 实名认证
- 权限管理

### 📱 消息推送
- 财务提醒推送
- 系统通知
- 重要事项提醒
- 消息历史记录

## 🛠 技术架构

### 前端技术栈
- **框架**: uni-app (Vue.js 2.x)
- **UI组件**: uView UI 1.x
- **状态管理**: Vuex
- **网络请求**: 封装的 request 模块
- **分页组件**: z-paging
- **图表组件**: mp-html

### 核心特性
- 🔄 **跨平台支持** - 一套代码，多端运行 (微信小程序、H5、APP)
- 🚀 **性能优化** - 分包加载、预加载、请求缓存
- 🔒 **安全保障** - Token认证、页面拦截、权限控制
- 📱 **响应式设计** - 适配各种屏幕尺寸
- 🔧 **组件化开发** - 高度复用的组件库

## 📁 项目结构

```
cl-kindergarten-wx/
├── api/                    # API接口管理
│   ├── api.js             # 接口定义
│   └── env.js             # 环境配置
├── components/            # 公共组件
│   └── appUpdate/         # 应用更新组件
├── config/               # 配置文件
│   └── config.js         # 系统配置
├── pages/                # 页面文件
│   ├── index/            # 首页模块
│   ├── my/               # 个人中心
│   ├── public/           # 公共页面
│   └── subPack/          # 分包页面
├── static/               # 静态资源
├── store/                # 状态管理
├── utils/                # 工具函数
│   ├── request.js        # 网络请求封装
│   ├── utils.js          # 通用工具
│   └── interceptor.js    # 拦截器
├── uni_modules/          # uni-app插件
├── App.vue               # 应用入口
├── main.js               # 主入口文件
├── pages.json            # 页面配置
└── manifest.json         # 应用配置
```

## 🚀 快速开始

### 环境要求
- Node.js >= 12.0.0
- HBuilderX 或 VS Code
- 微信开发者工具 (小程序开发)

### 安装步骤

1. **克隆项目**
```bash
git clone [项目地址]
cd cl-kindergarten-wx
```

2. **安装依赖**
```bash
npm install
```

3. **配置环境**
编辑 `api/env.js` 文件，配置服务器地址：
```javascript
let BASE_URL
if (process.env.NODE_ENV === 'development') {
  BASE_URL = 'https://your-dev-api.com'  // 开发环境
} else {
  BASE_URL = 'https://your-prod-api.com' // 生产环境
}
export default BASE_URL
```

4. **运行项目**
- 微信小程序：在 HBuilderX 中运行到微信开发者工具
- H5：在 HBuilderX 中运行到浏览器
- APP：在 HBuilderX 中运行到手机或模拟器

## 📖 开发指南

### API 接口调用

系统已全局注册 API 方法，页面中可直接调用：

```javascript
// 登录接口调用
this.$api.login({
  username: 'admin',
  password: '123456'
}).then(res => {
  if (res.code === 0) {
    // 登录成功处理
    console.log('登录成功', res.data)
  }
})

// 获取财务列表
this.$api.getList({
  page: 1,
  limit: 10
}).then(res => {
  // 处理列表数据
  this.dataList = res.data.list
})
```

### 页面拦截配置

在 `config/config.js` 中配置页面访问权限：

```javascript
let config = {
  // 白名单页面（无需登录即可访问）
  whiteList: [
    '/pages/public/login',
    '/pages/public/register'
  ],
  token: 'token',                           // Token存储键名
  login_page: '/pages/public/login'         // 登录页面路径
}
```

### 组件使用示例

```vue
<template>
  <view class="container">
    <!-- 使用 uView 组件 -->
    <u-button type="primary" @click="handleSubmit">提交</u-button>
    
    <!-- 使用分页组件 -->
    <z-paging ref="paging" @query="queryList">
      <view v-for="item in dataList" :key="item.id">
        {{ item.title }}
      </view>
    </z-paging>
  </view>
</template>
```

## 🔧 配置说明

### 分包配置
项目支持分包加载以优化小程序性能，配置在 `pages.json` 中：

```json
{
  "subPackages": [
    {
      "root": "pages/subPack",
      "pages": [
        {
          "path": "index/detail",
          "style": {
            "navigationBarTitleText": "详情"
          }
        }
      ]
    }
  ]
}
```

### 应用更新
支持 APP 在线更新功能，服务器需返回以下格式：

```json
{
  "code": 0,
  "platform": "android",
  "version": "1.3.0",
  "downUrl": "下载地址",
  "updateContent": "更新内容",
  "force": 0
}
```

## 🎨 UI 组件库

### uView UI 组件
项目集成了 uView UI 1.x 版本，提供丰富的组件：

- **基础组件**: Button、Icon、Image、Layout 等
- **表单组件**: Input、Textarea、Select、Checkbox 等
- **数据展示**: Table、Tag、Progress、Badge 等
- **反馈组件**: Modal、Toast、Loading、ActionSheet 等
- **导航组件**: Navbar、Tabbar、Tabs、Steps 等

### z-paging 分页组件
集成 z-paging 实现列表分页功能：

```vue
<template>
  <z-paging ref="paging" @query="queryList">
    <view v-for="item in dataList" :key="item.id" class="item">
      {{ item.title }}
    </view>
  </z-paging>
</template>

<script>
export default {
  data() {
    return {
      dataList: []
    }
  },
  methods: {
    queryList(pageNo, pageSize) {
      this.$api.getList({
        page: pageNo,
        limit: pageSize
      }).then(res => {
        this.$refs.paging.complete(res.data.list)
      })
    }
  }
}
</script>
```

## 🔐 安全机制

### Token 认证
- 登录成功后自动存储 Token
- 请求拦截器自动添加 Token 头部
- Token 过期自动跳转登录页

### 页面权限控制
- 白名单机制，配置无需登录的页面
- 自动检测登录状态
- 未登录用户自动重定向

### 数据安全
- 敏感数据加密传输
- 本地存储数据加密
- 防止 XSS 和 CSRF 攻击

## 📱 多端适配

### 支持平台
- **微信小程序** - 主要目标平台
- **H5** - 浏览器访问
- **APP** - 原生应用
- **支付宝小程序** - 扩展支持
- **百度小程序** - 扩展支持

### 适配说明
- 使用 uni-app 条件编译处理平台差异
- 响应式布局适配不同屏幕尺寸
- 统一的 API 调用方式

## 🚀 性能优化

### 代码优化
- 组件按需加载
- 图片懒加载
- 代码分包加载
- 预加载关键资源

### 网络优化
- 请求缓存机制
- 接口防重复调用
- 图片压缩优化
- CDN 资源加速

### 用户体验优化
- 骨架屏加载
- 下拉刷新/上拉加载
- 离线缓存
- 错误边界处理

## 🐛 常见问题

### 编译问题
**Q: 小程序编译报错 "unexpected character"**
A: 检查模板中是否有特殊字符，如 `<` `>` 需要转义为 `&lt;` `&gt;`

**Q: 依赖包版本冲突**
A: 删除 node_modules 重新安装，或使用 npm audit fix 修复

### 运行问题
**Q: 接口请求失败**
A: 检查 api/env.js 中的服务器地址配置是否正确

**Q: 页面白屏**
A: 检查路由配置和页面路径是否正确

### 功能问题
**Q: 登录后仍然跳转到登录页**
A: 检查 Token 是否正确存储，页面拦截器配置是否正确

## 🚀 部署指南

### 微信小程序部署
1. 在微信公众平台注册小程序账号
2. 获取小程序 AppID 并配置到 `manifest.json`
3. 在 HBuilderX 中发行到微信小程序
4. 使用微信开发者工具上传代码
5. 提交审核并发布

### H5 部署
1. 执行 `npm run build:h5` 构建生产版本
2. 将 `dist/build/h5` 目录部署到 Web 服务器
3. 配置 Nginx 或 Apache 服务器
4. 确保 HTTPS 访问（推荐）

### APP 部署
1. 配置原生应用信息到 `manifest.json`
2. 在 HBuilderX 中云打包或本地打包
3. 生成 APK/IPA 安装包
4. 发布到应用商店

## 📊 版本更新日志

### v1.2.0 (2024-01-15)
**新增功能**
- ✨ 新增财务数据导出功能
- ✨ 支持批量缴费操作
- ✨ 增加数据备份与恢复

**优化改进**
- 🚀 优化列表加载性能
- 💄 改进用户界面体验
- 🔧 修复已知问题

### v1.1.0 (2023-12-20)
**新增功能**
- ✨ 新增消息推送功能
- ✨ 支持多角色权限管理
- ✨ 增加财务报表统计

**优化改进**
- 🚀 提升系统响应速度
- 🔒 加强数据安全保护
- 📱 优化移动端适配

### v1.0.0 (2023-11-01)
**首次发布**
- 🎉 基础财务核算功能
- 🎉 用户认证与权限管理
- 🎉 个人中心功能
- 🎉 跨平台支持

## 🤝 贡献指南

1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🌟 特色功能

### 💡 智能化特性
- **🤖 智能提醒** - 基于业务规则的智能提醒系统
- **📊 数据可视化** - 丰富的图表展示，数据一目了然
- **🔍 智能搜索** - 全文搜索，快速定位所需信息
- **📱 离线支持** - 关键功能支持离线操作

### 🎨 用户体验
- **🎯 简洁界面** - 清爽简洁的界面设计
- **⚡ 快速响应** - 优化的性能，流畅的操作体验
- **🔄 实时同步** - 数据实时同步，多端一致
- **🌙 深色模式** - 支持深色主题，护眼舒适

### 🔧 技术亮点
- **📦 模块化设计** - 高度模块化，易于维护和扩展
- **🔌 插件化架构** - 支持插件扩展，功能可定制
- **🚀 性能优化** - 多项性能优化，确保流畅体验
- **🛡️ 安全防护** - 多层安全防护，保障数据安全

## 📞 联系我们

- **项目地址**: [GitHub仓库地址]
- **问题反馈**: [Issues页面]
- **技术支持**: [联系邮箱]
- **官方网站**: [www.chuanglian-kindergarten.com]
- **客服热线**: 400-xxx-xxxx

## 🙏 致谢

感谢以下开源项目的支持：
- [uni-app](https://uniapp.dcloud.io/) - 跨平台开发框架
- [uView UI](https://v1.uviewui.com/) - 优秀的 UI 组件库
- [z-paging](https://z-paging.zxlee.cn/) - 强大的分页组件
- [Vue.js](https://vuejs.org/) - 渐进式 JavaScript 框架

---

<div align="center">
  <p>© 2024 创联幼儿园核算系统. All rights reserved.</p>
  <p>🌟 让幼儿园财务管理更简单、更高效 🌟</p>
  <p>
    <a href="#top">回到顶部 ⬆️</a>
  </p>
</div>
