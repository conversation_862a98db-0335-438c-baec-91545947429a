<view class="content"><z-paging class="vue-ref" bind:query="__e" bind:input="__e" vue-id="8e0fffdc-1" data-ref="paging" value="{{dataList}}" data-event-opts="{{[['^query',[['queryList']]],['^input',[['__set_model',['','dataList','$event',[]]]]]]}}" bind:__l="__l" vue-slots="{{['default','top']}}"><z-tabs vue-id="{{('8e0fffdc-2')+','+('8e0fffdc-1')}}" list="{{tabList}}" data-event-opts="{{[['^change',[['tabChange']]]]}}" bind:change="__e" slot="top" bind:__l="__l"></z-tabs><block wx:for="{{dataList}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view data-event-opts="{{[['tap',[['itemClick',['$0'],[[['dataList','id',item.id]]]]]]]}}" class="con_box" bindtap="__e"><view class="photo"><image src="{{item.thumb}}" mode></image></view><view class="text_con"><view class="title">{{''+item.title+''}}</view><view class="addtime">{{''+item.add_time+''}}</view><view class="desc">{{''+item.desc+''}}</view></view></view></block></z-paging></view>