<view data-event-opts="{{[['tap',[['emptyViewClick',['$event']]]]]}}" class="{{['data-v-a664708e',(true)?'zp-container':'',(emptyViewFixed)?'zp-container-fixed':'']}}" style="{{$root.s0}}" bindtap="__e"><view class="zp-main data-v-a664708e"><block wx:if="{{!$root.g0}}"><image class="zp-main-image data-v-a664708e" style="{{$root.s1}}" src="{{emptyImg}}"></image></block><block wx:else><image class="zp-main-image data-v-a664708e" style="{{$root.s2}}" mode="aspectFit" src="{{emptyViewImg}}"></image></block><text class="zp-main-title data-v-a664708e" style="{{$root.s3}}">{{emptyViewText}}</text><block wx:if="{{showEmptyViewReload}}"><text data-event-opts="{{[['tap',[['reloadClick',['$event']]]]]}}" class="zp-main-error-btn data-v-a664708e" style="{{$root.s4}}" catchtap="__e">{{emptyViewReloadText}}</text></block></view></view>