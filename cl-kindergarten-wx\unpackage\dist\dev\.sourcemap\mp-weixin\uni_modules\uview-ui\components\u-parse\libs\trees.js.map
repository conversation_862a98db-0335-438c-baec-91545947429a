{"version": 3, "sources": ["webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-parse/libs/trees.vue?d02c", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-parse/libs/trees.vue?1c10", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-parse/libs/trees.vue?4e65", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-parse/libs/trees.vue?6a7f", "uni-app:///uni_modules/uview-ui/components/u-parse/libs/trees.vue", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-parse/libs/trees.vue?c9ab", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-parse/libs/trees.vue?ab2f", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-parse/libs/handler.wxs?ce62", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-parse/libs/handler.wxs?be7b"], "names": ["global", "components", "trees", "name", "data", "ctrl", "placeholder", "errorImg", "loadVideo", "c", "s", "props", "nodes", "lazyLoad", "loading", "mounted", "methods", "init", "ctx", "play", "contexts", "imgtap", "id", "src", "ignore", "current", "uni", "urls", "loadImg", "linkpress", "attrs", "appId", "path", "success", "title", "url", "fail", "error", "source", "i", "e", "target", "errMsg", "_loadVideo"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsT;AACtT;AACyD;AACL;AACa;;;AAGjE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,oRAAM;AACR,EAAE,6RAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wRAAU;AACZ;AACA;;AAEA;AACmP;AACnP,WAAW,qQAAM,iBAAiB,6QAAM;;AAExC;AACe,gF;;;;;;;;;;;;AC3Bf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC4EnnBA;AAAA;EAAA;IAAA;EAAA;AAAA;AAEA;AAAA,eACA;EACAC;IACAC;EACA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;IAEA;EACA;EACAC;IACAC;IACAC;IACAC;EAKA;EACAC;IACA;MAAA;IAAA;IACA;EACA;EAMAC;IACAC;MACA;QACA;UACA;QAmBA;UACA;UACA;YACAC,yCAEA,KAEA;UACA,mCACAA;UACA;YACAA;YACA;UACA;QACA;MACA;IAOA;IACAC;MACA;MACA,+CACA;QACA,kDACAC;MAAA;IACA;IACAC;MACA;MACA;QACA;UACAjB;YACAkB;YACAC;YACAC;cAAA;YAAA;UACA;QACAxB;QACA;QACA;UACA;YACAyB;UACAC;YACAD;YACAE;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;QAMA;MAKA;QAKA;MAIA;IACA;IACAC;MACA;QACAC;MACAA;QAAA;MAAA;MACA9B;MACA;MACA;QAEA;UACA;YACA+B;YACAC;UACA;QACA;QAEA;UACA;YACA,wBACA;cACAV;YACA;UACA;YAKAI;cACAtB;cACA6B;gBAAA,OACAP;kBACAQ;gBACA;cAAA;YACA;UAEA,OACAR;YACAS;YACAC;cACAV;gBACAS;cACA;YACA;UACA;QACA;MACA;IACA;IACAE;MACA;QACAC;QACAC;MACA;QACA;QACA;QACA,+CACA;QACA,uBACAC;MACA;QACA;QACA;MACA;MACA;QACAF;QACAG;QACAC;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3RA;AAAA;AAAA;AAAA;AAAi3B,CAAgB,k3BAAG,EAAC,C;;;;;;;;;;;ACAr4B;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA,wCAAkX,CAAgB,wbAAG,EAAC,C;;;;;;;;;;;;ACAtY;AAAe;AACf;AACA;AACA;;AAEA,M", "file": "uni_modules/uview-ui/components/u-parse/libs/trees.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./trees.vue?vue&type=template&id=e946a404&filter-modules=eyJoYW5kbGVyIjp7InR5cGUiOiJzY3JpcHQiLCJjb250ZW50IjoiIiwic3RhcnQiOjU0MjQsImF0dHJzIjp7Im1vZHVsZSI6ImhhbmRsZXIiLCJsYW5nIjoid3hzIiwic3JjIjoiLi9oYW5kbGVyLnd4cyJ9LCJlbmQiOjU0MjR9fQ%3D%3D&\"\nvar renderjs\nimport script from \"./trees.vue?vue&type=script&lang=js&\"\nexport * from \"./trees.vue?vue&type=script&lang=js&\"\nimport style0 from \"./trees.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\n/* custom blocks */\nimport block0 from \"./handler.wxs?vue&type=custom&index=0&blockType=script&issuerPath=E%3A%5CIdeaProject%5Ckindergarten_accounting%5Ccl-kindergarten-wx%5Cuni_modules%5Cuview-ui%5Ccomponents%5Cu-parse%5Clibs%5Ctrees.vue&module=handler&lang=wxs\"\nif (typeof block0 === 'function') block0(component)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-parse/libs/trees.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./trees.vue?vue&type=template&id=e946a404&filter-modules=eyJoYW5kbGVyIjp7InR5cGUiOiJzY3JpcHQiLCJjb250ZW50IjoiIiwic3RhcnQiOjU0MjQsImF0dHJzIjp7Im1vZHVsZSI6ImhhbmRsZXIiLCJsYW5nIjoid3hzIiwic3JjIjoiLi9oYW5kbGVyLnd4cyJ9LCJlbmQiOjU0MjR9fQ%3D%3D&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./trees.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./trees.vue?vue&type=script&lang=js&\"", "<template>\n\t<view :class=\"'interlayer '+(c||'')\" :style=\"s\">\n\t\t<block v-for=\"(n, i) in nodes\" v-bind:key=\"i\">\n\t\t\t<!--图片-->\n\t\t\t<view v-if=\"n.name=='img'\" :class=\"'_img '+n.attrs.class\" :style=\"n.attrs.style\" :data-attrs=\"n.attrs\" @tap.stop=\"imgtap\">\n\t\t\t\t<rich-text v-if=\"ctrl[i]!=0\" :nodes=\"[{attrs:{src:loading&&(ctrl[i]||0)<2?loading:(lazyLoad&&!ctrl[i]?placeholder:(ctrl[i]==3?errorImg:n.attrs.src||'')),alt:n.attrs.alt||'',width:n.attrs.width||'',style:'-webkit-touch-callout:none;max-width:100%;display:block'+(n.attrs.height?';height:'+n.attrs.height:'')},name:'img'}]\" />\n\t\t\t\t<image class=\"_image\" :src=\"lazyLoad&&!ctrl[i]?placeholder:n.attrs.src\" :lazy-load=\"lazyLoad\"\n\t\t\t\t :show-menu-by-longpress=\"!n.attrs.ignore\" :data-i=\"i\" :data-index=\"n.attrs.i\" data-source=\"img\" @load=\"loadImg\"\n\t\t\t\t @error=\"error\" />\n\t\t\t</view>\n\t\t\t<!--文本-->\n\t\t\t<text v-else-if=\"n.type=='text'\" decode>{{n.text}}</text>\n\t\t\t<!--#ifndef MP-BAIDU-->\n\t\t\t<text v-else-if=\"n.name=='br'\">\\n</text>\n\t\t\t<!--#endif-->\n\t\t\t<!--视频-->\n\t\t\t<view v-else-if=\"((n.lazyLoad&&!n.attrs.autoplay)||(n.name=='video'&&!loadVideo))&&ctrl[i]==undefined\" :id=\"n.attrs.id\"\n\t\t\t :class=\"'_video '+(n.attrs.class||'')\" :style=\"n.attrs.style\" :data-i=\"i\" @tap.stop=\"_loadVideo\" />\n\t\t\t<video v-else-if=\"n.name=='video'\" :id=\"n.attrs.id\" :class=\"n.attrs.class\" :style=\"n.attrs.style\" :autoplay=\"n.attrs.autoplay||ctrl[i]==0\"\n\t\t\t :controls=\"n.attrs.controls\" :loop=\"n.attrs.loop\" :muted=\"n.attrs.muted\" :poster=\"n.attrs.poster\" :src=\"n.attrs.source[ctrl[i]||0]\"\n\t\t\t :unit-id=\"n.attrs['unit-id']\" :data-id=\"n.attrs.id\" :data-i=\"i\" data-source=\"video\" @error=\"error\" @play=\"play\" />\n\t\t\t<!--音频-->\n\t\t\t<audio v-else-if=\"n.name=='audio'\" :ref=\"n.attrs.id\" :class=\"n.attrs.class\" :style=\"n.attrs.style\" :author=\"n.attrs.author\"\n\t\t\t :autoplay=\"n.attrs.autoplay\" :controls=\"n.attrs.controls\" :loop=\"n.attrs.loop\" :name=\"n.attrs.name\" :poster=\"n.attrs.poster\"\n\t\t\t :src=\"n.attrs.source[ctrl[i]||0]\" :data-i=\"i\" :data-id=\"n.attrs.id\" data-source=\"audio\" @error.native=\"error\"\n\t\t\t @play.native=\"play\" />\n\t\t\t<!--链接-->\n\t\t\t<view v-else-if=\"n.name=='a'\" :id=\"n.attrs.id\" :class=\"'_a '+(n.attrs.class||'')\" hover-class=\"_hover\" :style=\"n.attrs.style\"\n\t\t\t :data-attrs=\"n.attrs\" @tap.stop=\"linkpress\">\n\t\t\t\t<trees class=\"_span\" c=\"_span\" :nodes=\"n.children\" />\n\t\t\t</view>\n\t\t\t<!--广告-->\n\t\t\t<!--<ad v-else-if=\"n.name=='ad'\" :class=\"n.attrs.class\" :style=\"n.attrs.style\" :unit-id=\"n.attrs['unit-id']\" :appid=\"n.attrs.appid\" :apid=\"n.attrs.apid\" :type=\"n.attrs.type\" :adpid=\"n.attrs.adpid\" data-source=\"ad\" @error=\"error\" />-->\n\t\t\t<!--列表-->\n\t\t\t<view v-else-if=\"n.name=='li'\" :id=\"n.attrs.id\" :class=\"n.attrs.class\" :style=\"(n.attrs.style||'')+';display:flex;flex-direction:row'\">\n\t\t\t\t<view v-if=\"n.type=='ol'\" class=\"_ol-bef\">{{n.num}}</view>\n\t\t\t\t<view v-else class=\"_ul-bef\">\n\t\t\t\t\t<view v-if=\"n.floor%3==0\" class=\"_ul-p1\">█</view>\n\t\t\t\t\t<view v-else-if=\"n.floor%3==2\" class=\"_ul-p2\" />\n\t\t\t\t\t<view v-else class=\"_ul-p1\" style=\"border-radius:50%\">█</view>\n\t\t\t\t</view>\n\t\t\t\t<trees class=\"_li\" c=\"_li\" :nodes=\"n.children\" :lazyLoad=\"lazyLoad\" :loading=\"loading\" />\n\t\t\t</view>\n\t\t\t<!--表格-->\n\t\t\t<view v-else-if=\"n.name=='table'&&n.c&&n.flag\" :id=\"n.attrs.id\" :class=\"n.attrs.class\" :style=\"(n.attrs.style||'')+';display:grid'\">\n\t\t\t\t<trees v-for=\"(cell,n) in n.children\" v-bind:key=\"n\" :class=\"cell.attrs.class\" :c=\"cell.attrs.class\" :style=\"cell.attrs.style\"\n\t\t\t\t :s=\"cell.attrs.style\" :nodes=\"cell.children\" />\n\t\t\t</view>\n\t\t\t<view v-else-if=\"n.name=='table'&&n.c\" :id=\"n.attrs.id\" :class=\"n.attrs.class\" :style=\"(n.attrs.style||'')+';display:table'\">\n\t\t\t\t<view v-for=\"(tbody, o) in n.children\" v-bind:key=\"o\" :class=\"tbody.attrs.class\" :style=\"(tbody.attrs.style||'')+(tbody.name[0]=='t'?';display:table-'+(tbody.name=='tr'?'row':'row-group'):'')\">\n\t\t\t\t\t<view v-for=\"(tr, p) in tbody.children\" v-bind:key=\"p\" :class=\"tr.attrs.class\" :style=\"(tr.attrs.style||'')+(tr.name[0]=='t'?';display:table-'+(tr.name=='tr'?'row':'cell'):'')\">\n\t\t\t\t\t\t<trees v-if=\"tr.name=='td'\" :nodes=\"tr.children\" />\n\t\t\t\t\t\t<trees v-else v-for=\"(td, q) in tr.children\" v-bind:key=\"q\" :class=\"td.attrs.class\" :c=\"td.attrs.class\" :style=\"(td.attrs.style||'')+(td.name[0]=='t'?';display:table-'+(td.name=='tr'?'row':'cell'):'')\"\n\t\t\t\t\t\t :s=\"(td.attrs.style||'')+(td.name[0]=='t'?';display:table-'+(td.name=='tr'?'row':'cell'):'')\" :nodes=\"td.children\" />\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<!--#ifdef APP-PLUS-->\n\t\t\t<iframe v-else-if=\"n.name=='iframe'\" :style=\"n.attrs.style\" :allowfullscreen=\"n.attrs.allowfullscreen\" :frameborder=\"n.attrs.frameborder\"\n\t\t\t :width=\"n.attrs.width\" :height=\"n.attrs.height\" :src=\"n.attrs.src\" />\n\t\t\t<embed v-else-if=\"n.name=='embed'\" :style=\"n.attrs.style\" :width=\"n.attrs.width\" :height=\"n.attrs.height\" :src=\"n.attrs.src\" />\n\t\t\t<!--#endif-->\n\t\t\t<!--富文本-->\n\t\t\t<!--#ifdef MP-WEIXIN || MP-QQ || APP-PLUS-->\n\t\t\t<rich-text v-else-if=\"handler.use(n)\" :id=\"n.attrs.id\" :class=\"'_p __'+n.name\" :nodes=\"[n]\" />\n\t\t\t<!--#endif-->\n\t\t\t<!--#ifndef MP-WEIXIN || MP-QQ || APP-PLUS-->\n\t\t\t<rich-text v-else-if=\"!n.c\" :id=\"n.attrs.id\" :nodes=\"[n]\" style=\"display:inline\" />\n\t\t\t<!--#endif-->\n\t\t\t<trees v-else :class=\"(n.attrs.id||'')+' _'+n.name+' '+(n.attrs.class||'')\" :c=\"(n.attrs.id||'')+' _'+n.name+' '+(n.attrs.class||'')\"\n\t\t\t :style=\"n.attrs.style\" :s=\"n.attrs.style\" :nodes=\"n.children\" :lazyLoad=\"lazyLoad\" :loading=\"loading\" />\n\t\t</block>\n\t</view>\n</template>\n<script module=\"handler\" lang=\"wxs\" src=\"./handler.wxs\"></script>\n<script>\n\tglobal.Parser = {};\n\timport trees from './trees'\n\tconst errorImg = require('../libs/config.js').errorImg;\n\texport default {\n\t\tcomponents: {\n\t\t\ttrees\n\t\t},\n\t\tname: 'trees',\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tctrl: [],\n\t\t\t\tplaceholder: 'data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"300\" height=\"225\"/>',\n\t\t\t\terrorImg,\n\t\t\t\tloadVideo: typeof plus == 'undefined',\n\t\t\t\t// #ifndef MP-ALIPAY\n\t\t\t\tc: '',\n\t\t\t\ts: ''\n\t\t\t\t// #endif\n\t\t\t}\n\t\t},\n\t\tprops: {\n\t\t\tnodes: Array,\n\t\t\tlazyLoad: Boolean,\n\t\t\tloading: String,\n\t\t\t// #ifdef MP-ALIPAY\n\t\t\tc: String,\n\t\t\ts: String\n\t\t\t// #endif\n\t\t},\n\t\tmounted() {\n\t\t\tfor (this.top = this.$parent; this.top.$options.name != 'parser'; this.top = this.top.$parent);\n\t\t\tthis.init();\n\t\t},\n\t\t// #ifdef APP-PLUS\n\t\tbeforeDestroy() {\n\t\t\tthis.observer && this.observer.disconnect();\n\t\t},\n\t\t// #endif\n\t\tmethods: {\n\t\t\tinit() {\n\t\t\t\tfor (var i = this.nodes.length, n; n = this.nodes[--i];) {\n\t\t\t\t\tif (n.name == 'img') {\n\t\t\t\t\t\tthis.top.imgList.setItem(n.attrs.i, n.attrs['original-src'] || n.attrs.src);\n\t\t\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\t\t\tif (this.lazyLoad && !this.observer) {\n\t\t\t\t\t\t\tthis.observer = uni.createIntersectionObserver(this).relativeToViewport({\n\t\t\t\t\t\t\t\ttop: 500,\n\t\t\t\t\t\t\t\tbottom: 500\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\tthis.observer.observe('._img', res => {\n\t\t\t\t\t\t\t\t\tif (res.intersectionRatio) {\n\t\t\t\t\t\t\t\t\t\tfor (var j = this.nodes.length; j--;)\n\t\t\t\t\t\t\t\t\t\t\tif (this.nodes[j].name == 'img')\n\t\t\t\t\t\t\t\t\t\t\t\tthis.$set(this.ctrl, j, 1);\n\t\t\t\t\t\t\t\t\t\tthis.observer.disconnect();\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t}, 0)\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// #endif\n\t\t\t\t\t} else if (n.name == 'video' || n.name == 'audio') {\n\t\t\t\t\t\tvar ctx;\n\t\t\t\t\t\tif (n.name == 'video') {\n\t\t\t\t\t\t\tctx = uni.createVideoContext(n.attrs.id\n\t\t\t\t\t\t\t\t// #ifndef MP-BAIDU\n\t\t\t\t\t\t\t\t, this\n\t\t\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t} else if (this.$refs[n.attrs.id])\n\t\t\t\t\t\t\tctx = this.$refs[n.attrs.id][0];\n\t\t\t\t\t\tif (ctx) {\n\t\t\t\t\t\t\tctx.id = n.attrs.id;\n\t\t\t\t\t\t\tthis.top.videoContexts.push(ctx);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\t// APP 上避免 video 错位需要延时渲染\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.loadVideo = true;\n\t\t\t\t}, 1000)\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\tplay(e) {\n\t\t\t\tvar contexts = this.top.videoContexts;\n\t\t\t\tif (contexts.length > 1 && this.top.autopause)\n\t\t\t\t\tfor (var i = contexts.length; i--;)\n\t\t\t\t\t\tif (contexts[i].id != e.currentTarget.dataset.id)\n\t\t\t\t\t\t\tcontexts[i].pause();\n\t\t\t},\n\t\t\timgtap(e) {\n\t\t\t\tvar attrs = e.currentTarget.dataset.attrs;\n\t\t\t\tif (!attrs.ignore) {\n\t\t\t\t\tvar preview = true,\n\t\t\t\t\t\tdata = {\n\t\t\t\t\t\t\tid: e.target.id,\n\t\t\t\t\t\t\tsrc: attrs.src,\n\t\t\t\t\t\t\tignore: () => preview = false\n\t\t\t\t\t\t};\n\t\t\t\t\tglobal.Parser.onImgtap && global.Parser.onImgtap(data);\n\t\t\t\t\tthis.top.$emit('imgtap', data);\n\t\t\t\t\tif (preview) {\n\t\t\t\t\t\tvar urls = this.top.imgList,\n\t\t\t\t\t\t\tcurrent = urls[attrs.i] ? parseInt(attrs.i) : (urls = [attrs.src], 0);\n\t\t\t\t\t\tuni.previewImage({\n\t\t\t\t\t\t\tcurrent,\n\t\t\t\t\t\t\turls\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\tloadImg(e) {\n\t\t\t\tvar i = e.currentTarget.dataset.i;\n\t\t\t\tif (this.lazyLoad && !this.ctrl[i]) {\n\t\t\t\t\t// #ifdef QUICKAPP-WEBVIEW\n\t\t\t\t\tthis.$set(this.ctrl, i, 0);\n\t\t\t\t\tthis.$nextTick(function() {\n\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t// #ifndef APP-PLUS\n\t\t\t\t\t\tthis.$set(this.ctrl, i, 1);\n\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t// #ifdef QUICKAPP-WEBVIEW\n\t\t\t\t\t})\n\t\t\t\t\t// #endif\n\t\t\t\t} else if (this.loading && this.ctrl[i] != 2) {\n\t\t\t\t\t// #ifdef QUICKAPP-WEBVIEW\n\t\t\t\t\tthis.$set(this.ctrl, i, 0);\n\t\t\t\t\tthis.$nextTick(function() {\n\t\t\t\t\t\t// #endif\n\t\t\t\t\t\tthis.$set(this.ctrl, i, 2);\n\t\t\t\t\t\t// #ifdef QUICKAPP-WEBVIEW\n\t\t\t\t\t})\n\t\t\t\t\t// #endif\n\t\t\t\t}\n\t\t\t},\n\t\t\tlinkpress(e) {\n\t\t\t\tvar jump = true,\n\t\t\t\t\tattrs = e.currentTarget.dataset.attrs;\n\t\t\t\tattrs.ignore = () => jump = false;\n\t\t\t\tglobal.Parser.onLinkpress && global.Parser.onLinkpress(attrs);\n\t\t\t\tthis.top.$emit('linkpress', attrs);\n\t\t\t\tif (jump) {\n\t\t\t\t\t// #ifdef MP\n\t\t\t\t\tif (attrs['app-id']) {\n\t\t\t\t\t\treturn uni.navigateToMiniProgram({\n\t\t\t\t\t\t\tappId: attrs['app-id'],\n\t\t\t\t\t\t\tpath: attrs.path\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t\t// #endif\n\t\t\t\t\tif (attrs.href) {\n\t\t\t\t\t\tif (attrs.href[0] == '#') {\n\t\t\t\t\t\t\tif (this.top.useAnchor)\n\t\t\t\t\t\t\t\tthis.top.navigateTo({\n\t\t\t\t\t\t\t\t\tid: attrs.href.substring(1)\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t} else if (attrs.href.indexOf('http') == 0 || attrs.href.indexOf('//') == 0) {\n\t\t\t\t\t\t\t// #ifdef APP-PLUS\n\t\t\t\t\t\t\tplus.runtime.openWeb(attrs.href);\n\t\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t\t// #ifndef APP-PLUS\n\t\t\t\t\t\t\tuni.setClipboardData({\n\t\t\t\t\t\t\t\tdata: attrs.href,\n\t\t\t\t\t\t\t\tsuccess: () =>\n\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\ttitle: '链接已复制'\n\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t// #endif\n\t\t\t\t\t\t} else\n\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\turl: attrs.href,\n\t\t\t\t\t\t\t\tfail() {\n\t\t\t\t\t\t\t\t\tuni.switchTab({\n\t\t\t\t\t\t\t\t\t\turl: attrs.href,\n\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\terror(e) {\n\t\t\t\tvar target = e.currentTarget,\n\t\t\t\t\tsource = target.dataset.source,\n\t\t\t\t\ti = target.dataset.i;\n\t\t\t\tif (source == 'video' || source == 'audio') {\n\t\t\t\t\t// 加载其他 source\n\t\t\t\t\tvar index = this.ctrl[i] ? this.ctrl[i].i + 1 : 1;\n\t\t\t\t\tif (index < this.nodes[i].attrs.source.length)\n\t\t\t\t\t\tthis.$set(this.ctrl, i, index);\n\t\t\t\t\tif (e.detail.__args__)\n\t\t\t\t\t\te.detail = e.detail.__args__[0];\n\t\t\t\t} else if (errorImg && source == 'img') {\n\t\t\t\t\tthis.top.imgList.setItem(target.dataset.index, errorImg);\n\t\t\t\t\tthis.$set(this.ctrl, i, 3);\n\t\t\t\t}\n\t\t\t\tthis.top && this.top.$emit('error', {\n\t\t\t\t\tsource,\n\t\t\t\t\ttarget,\n\t\t\t\t\terrMsg: e.detail.errMsg\n\t\t\t\t});\n\t\t\t},\n\t\t\t_loadVideo(e) {\n\t\t\t\tthis.$set(this.ctrl, e.target.dataset.i, 0);\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t/* 在这里引入自定义样式 */\n\n\t/* 链接和图片效果 */\n\t._a {\n\t\tdisplay: inline;\n\t\tpadding: 1.5px 0 1.5px 0;\n\t\tcolor: #366092;\n\t\tword-break: break-all;\n\t}\n\n\t._hover {\n\t\ttext-decoration: underline;\n\t\topacity: 0.7;\n\t}\n\n\t._img {\n\t\tdisplay: inline-block;\n\t\tmax-width: 100%;\n\t\toverflow: hidden;\n\t}\n\n\t/* #ifdef MP-WEIXIN */\n\t:host {\n\t\tdisplay: inline;\n\t}\n\n\t/* #endif */\n\n\t/* #ifndef MP-ALIPAY || APP-PLUS */\n\t.interlayer {\n\t\tdisplay: inherit;\n\t\tflex-direction: inherit;\n\t\tflex-wrap: inherit;\n\t\talign-content: inherit;\n\t\talign-items: inherit;\n\t\tjustify-content: inherit;\n\t\twidth: 100%;\n\t\twhite-space: inherit;\n\t}\n\n\t/* #endif */\n\n\t._b,\n\t._strong {\n\t\tfont-weight: bold;\n\t}\n\n\t/* #ifndef MP-ALIPAY */\n\t._blockquote,\n\t._div,\n\t._p,\n\t._ol,\n\t._ul,\n\t._li {\n\t\tdisplay: block;\n\t}\n\n\t/* #endif */\n\n\t._code {\n\t\tfont-family: monospace;\n\t}\n\n\t._del {\n\t\ttext-decoration: line-through;\n\t}\n\n\t._em,\n\t._i {\n\t\tfont-style: italic;\n\t}\n\n\t._h1 {\n\t\tfont-size: 2em;\n\t}\n\n\t._h2 {\n\t\tfont-size: 1.5em;\n\t}\n\n\t._h3 {\n\t\tfont-size: 1.17em;\n\t}\n\n\t._h5 {\n\t\tfont-size: 0.83em;\n\t}\n\n\t._h6 {\n\t\tfont-size: 0.67em;\n\t}\n\n\t._h1,\n\t._h2,\n\t._h3,\n\t._h4,\n\t._h5,\n\t._h6 {\n\t\tdisplay: block;\n\t\tfont-weight: bold;\n\t}\n\n\t._image {\n\t\tdisplay: block;\n\t\twidth: 100%;\n\t\theight: 360px;\n\t\tmargin-top: -360px;\n\t\topacity: 0;\n\t}\n\n\t._ins {\n\t\ttext-decoration: underline;\n\t}\n\n\t._li {\n\t\tflex: 1;\n\t\twidth: 0;\n\t}\n\n\t._ol-bef {\n\t\twidth: 36px;\n\t\tmargin-right: 5px;\n\t\ttext-align: right;\n\t}\n\n\t._ul-bef {\n\t\tdisplay: block;\n\t\tmargin: 0 12px 0 23px;\n\t\tline-height: normal;\n\t}\n\n\t._ol-bef,\n\t._ul-bef {\n\t\tflex: none;\n\t\tuser-select: none;\n\t}\n\n\t._ul-p1 {\n\t\tdisplay: inline-block;\n\t\twidth: 0.3em;\n\t\theight: 0.3em;\n\t\toverflow: hidden;\n\t\tline-height: 0.3em;\n\t}\n\n\t._ul-p2 {\n\t\tdisplay: inline-block;\n\t\twidth: 0.23em;\n\t\theight: 0.23em;\n\t\tborder: 0.05em solid black;\n\t\tborder-radius: 50%;\n\t}\n\n\t._q::before {\n\t\tcontent: '\"';\n\t}\n\n\t._q::after {\n\t\tcontent: '\"';\n\t}\n\n\t._sub {\n\t\tfont-size: smaller;\n\t\tvertical-align: sub;\n\t}\n\n\t._sup {\n\t\tfont-size: smaller;\n\t\tvertical-align: super;\n\t}\n\n\t/* #ifdef MP-ALIPAY || APP-PLUS || QUICKAPP-WEBVIEW */\n\t._abbr,\n\t._b,\n\t._code,\n\t._del,\n\t._em,\n\t._i,\n\t._ins,\n\t._label,\n\t._q,\n\t._span,\n\t._strong,\n\t._sub,\n\t._sup {\n\t\tdisplay: inline;\n\t}\n\n\t/* #endif */\n\n\t/* #ifdef MP-WEIXIN || MP-QQ */\n\t.__bdo,\n\t.__bdi,\n\t.__ruby,\n\t.__rt {\n\t\tdisplay: inline-block;\n\t}\n\n\t/* #endif */\n\t._video {\n\t\tposition: relative;\n\t\tdisplay: inline-block;\n\t\twidth: 300px;\n\t\theight: 225px;\n\t\tbackground-color: black;\n\t}\n\n\t._video::after {\n\t\tposition: absolute;\n\t\ttop: 50%;\n\t\tleft: 50%;\n\t\tmargin: -15px 0 0 -15px;\n\t\tcontent: '';\n\t\tborder-color: transparent transparent transparent white;\n\t\tborder-style: solid;\n\t\tborder-width: 15px 0 15px 30px;\n\t}\n</style>\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./trees.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./trees.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753690215140\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-filter-loader\\\\index.js!./handler.wxs?vue&type=custom&index=0&blockType=script&issuerPath=E%3A%5CIdeaProject%5Ckindergarten_accounting%5Ccl-kindergarten-wx%5Cuni_modules%5Cuview-ui%5Ccomponents%5Cu-parse%5Clibs%5Ctrees.vue&module=handler&lang=wxs\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-filter-loader\\\\index.js!./handler.wxs?vue&type=custom&index=0&blockType=script&issuerPath=E%3A%5CIdeaProject%5Ckindergarten_accounting%5Ccl-kindergarten-wx%5Cuni_modules%5Cuview-ui%5Ccomponents%5Cu-parse%5Clibs%5Ctrees.vue&module=handler&lang=wxs\"", "export default function (Component) {\n       if(!Component.options.wxsCallMethods){\n         Component.options.wxsCallMethods = []\n       }\n       \n     }"], "sourceRoot": ""}