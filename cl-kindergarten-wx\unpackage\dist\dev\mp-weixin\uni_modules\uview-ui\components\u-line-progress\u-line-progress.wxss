@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.u-progress.data-v-26adb0f2 {
  overflow: hidden;
  height: 15px;
  display: inline-flex;
  align-items: center;
  width: 100%;
  border-radius: 100rpx;
}
.u-active.data-v-26adb0f2 {
  width: 0;
  height: 100%;
  align-items: center;
  display: flex;
  flex-direction: row;
  justify-items: flex-end;
  justify-content: space-around;
  font-size: 20rpx;
  color: #ffffff;
  transition: all 0.4s ease;
}
.u-striped.data-v-26adb0f2 {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 39px 39px;
}
.u-striped-active.data-v-26adb0f2 {
  -webkit-animation: progress-stripes-data-v-26adb0f2 2s linear infinite;
          animation: progress-stripes-data-v-26adb0f2 2s linear infinite;
}
@-webkit-keyframes progress-stripes-data-v-26adb0f2 {
0% {
    background-position: 0 0;
}
100% {
    background-position: 39px 0;
}
}
@keyframes progress-stripes-data-v-26adb0f2 {
0% {
    background-position: 0 0;
}
100% {
    background-position: 39px 0;
}
}
