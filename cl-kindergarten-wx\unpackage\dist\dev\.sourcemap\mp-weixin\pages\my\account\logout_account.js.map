{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/pages/my/account/logout_account.vue?d098", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/pages/my/account/logout_account.vue?9192", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/pages/my/account/logout_account.vue?e942", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/pages/my/account/logout_account.vue?d693", "uni-app:///pages/my/account/logout_account.vue", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/pages/my/account/logout_account.vue?447e", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/pages/my/account/logout_account.vue?3b73"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "content", "show", "agree", "showModal", "onLoad", "onShow", "onReady", "methods", "getPage", "id", "confirme", "confirmOperation", "closeShowModal"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,uBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA2H;AAC3H;AACkE;AACL;AACc;;;AAG3E;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,yFAAM;AACR,EAAE,kGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,mQAEN;AACP,KAAK;AACL;AACA,aAAa,uPAEN;AACP,KAAK;AACL;AACA,aAAa,iPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChDA;AAAA;AAAA;AAAA;AAAwmB,CAAgB,koBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACqB5nB;;;;;;;;;;;;;;;;;;;;;eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC,2BACA;EACAC,6BACA;EACAC;IAEAC;MAAA;MACA;QACAC;MACA;MACA;QACA;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;QACA;UACA;QACA;MACA;IAEA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACnEA;AAAA;AAAA;AAAA;AAAmpC,CAAgB,ynCAAG,EAAC,C;;;;;;;;;;;ACAvqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/my/account/logout_account.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/my/account/logout_account.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./logout_account.vue?vue&type=template&id=********&\"\nvar renderjs\nimport script from \"./logout_account.vue?vue&type=script&lang=js&\"\nexport * from \"./logout_account.vue?vue&type=script&lang=js&\"\nimport style0 from \"./logout_account.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/my/account/logout_account.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./logout_account.vue?vue&type=template&id=********&\"", "var components\ntry {\n  components = {\n    uParse: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-parse/u-parse\" */ \"@/uni_modules/uview-ui/components/u-parse/u-parse.vue\"\n      )\n    },\n    uCheckbox: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-checkbox/u-checkbox\" */ \"@/uni_modules/uview-ui/components/u-checkbox/u-checkbox.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-button/u-button\" */ \"@/uni_modules/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n    uModal: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-modal/u-modal\" */ \"@/uni_modules/uview-ui/components/u-modal/u-modal.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./logout_account.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./logout_account.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"content\">\n\t\t<view class=\"u-content\">\n\t\t\t<u-parse :html=\"content\"></u-parse>\t\n\t\t</view>\r\n\t\r\n\t\t\r\n\t\t<view class=\"tipsArea\">\r\n\t\t\t<view class=\"operation\">\r\n\t\t\t<u-checkbox v-model=\"agree\">我同意<<注销协议>>，提交后帐号将不再恢复</u-checkbox>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"btn\">\r\n\t\t\t\t<u-button type=\"primary\" style=\"width: 98%;margin-bottom: 15rpx;\" @click=\"confirme\">注销帐号</u-button>\r\n\t\t\t\t\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<u-modal v-model=\"showModal\" showCancelButton title=\"注销\" content='确定要注销帐号吗?' @cancel=\"closeShowModal\" @confirm=\"confirmOperation\"></u-modal>\n\t</view>\n</template>\n<script>\nimport {toast, clearStorageSync, setStorageSync, getStorageSync, useRouter} from '@/utils/utils.js'\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tcontent: '',\r\n\t\t\tshow:true,\r\n\t\t\tagree : false,\r\n\t\t\tshowModal: false\n\t\t}\n\t},\n\tonLoad(op) {\n\t\t//this.id  = op.id\r\n\t\tthis.getPage()\n\t},\n\tonShow() {\n\t},\n\tonReady() {\n\t},\n\tmethods: {\r\n\t\t\n\t\tgetPage(){\r\n\t\t\tconst param={\r\n\t\t\t\tid:6\r\n\t\t\t}\r\n\t\t\tthis.$api.page(param).then(res => {\r\n\t\t\t\tif (res.code ==1 ) {\r\n\t\t\t\t\tthis.content = res.data.content\r\n\t\t\t\t} \r\n\t\t\t})\r\n\t\t},\r\n\t\tconfirme(){\r\n\t\t\tif(!this.agree) return toast('请查看并同意相关协议');\r\n\t\t\tthis.showModal = true\r\n\t\t},\r\n\t\tconfirmOperation() {\r\n\t\t\tthis.$api.logout_account().then(res => {\r\n\t\t\t\tif (res.code ==0 ) {\r\n\t\t\t\t\ttoast(res.msg);\r\n\t\t\t\t} \r\n\t\t\t})\r\n\t\t\t\r\n\t\t},\r\n\t\tcloseShowModal(){\r\n\t\t\tthis.showModal = false\r\n\t\t}\n\t}\n}\n</script>\n<style lang=\"scss\">\t\n\t.u-content{\r\n\t\tpadding: 20rpx;\r\n\t\tcolor: $u-content-color;\r\n\t\tfont-size: 28rpx;\r\n\t\tline-height: 1.8;\t\r\n\t\tpadding-bottom: 250rpx;\r\n\t}\r\n\t.tipsArea{position: fixed; bottom: 0;background: #fdf6ec;width: 100%;}\r\n\t.operation{padding:20rpx;padding-top: 20rpx;\r\n\t\t\r\n\t}\n</style>\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./logout_account.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./logout_account.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // *************\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}