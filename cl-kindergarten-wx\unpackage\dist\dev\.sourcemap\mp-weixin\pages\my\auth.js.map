{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/pages/my/auth.vue?2023", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/pages/my/auth.vue?06d9", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/pages/my/auth.vue?a4d9", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/pages/my/auth.vue?1003", "uni-app:///pages/my/auth.vue", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/pages/my/auth.vue?5f79", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/pages/my/auth.vue?4e95"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "action", "fileList", "form", "cardno", "photo", "name", "form_data", "floder", "rules", "required", "message", "trigger", "onReady", "methods", "submit", "_uploadPhoto_data", "url", "setTimeout", "uni", "console"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACc;;;AAGjE;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2OAEN;AACP,KAAK;AACL;AACA,aAAa,+TAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,uPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrDA;AAAA;AAAA;AAAA;AAA8lB,CAAgB,wnBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACqBlnB;;;;;;;;;;;;;;;;;;;;;eACA;EACAC;IACA;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MAEAC;QACAC;MACA;MACAC;QACAL,SACA;UACAM;UACAC;UACA;UACAC;QACA;MACA,aACA,CACA;QACAF;QACAC;QACA;QACAC;MACA,EACA;IAGA;EACA;EACAC;IACA;EACA;EAEAC;IACAC;MAAA;MACA;QACA;UAEA;UACA;UACA;UAEA;YACAC;cACAC;YACA;UACA;UAEA,sDACA;YACA;cACA;cACAC;gBACAC;gBACAA;cACA;cAEAC;YACA;cACA;cACAA;YACA;UACA;UAEAA;QACA;UAEAA;QACA;MACA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrGA;AAAA;AAAA;AAAA;AAAyoC,CAAgB,+mCAAG,EAAC,C;;;;;;;;;;;ACA7pC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/my/auth.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/my/auth.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./auth.vue?vue&type=template&id=5ddc5642&\"\nvar renderjs\nimport script from \"./auth.vue?vue&type=script&lang=js&\"\nexport * from \"./auth.vue?vue&type=script&lang=js&\"\nimport style0 from \"./auth.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/my/auth.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./auth.vue?vue&type=template&id=5ddc5642&\"", "var components\ntry {\n  components = {\n    uForm: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-form/u-form\" */ \"@/uni_modules/uview-ui/components/u-form/u-form.vue\"\n      )\n    },\n    uFormItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-form-item/u-form-item\" */ \"@/uni_modules/uview-ui/components/u-form-item/u-form-item.vue\"\n      )\n    },\n    uInput: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-input/u-input\" */ \"@/uni_modules/uview-ui/components/u-input/u-input.vue\"\n      )\n    },\n    uUpload: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-upload/u-upload\" */ \"@/uni_modules/uview-ui/components/u-upload/u-upload.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-button/u-button\" */ \"@/uni_modules/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./auth.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./auth.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"content\">\n\t\t<u-form :model=\"form\" ref=\"uForm\" :error-type=\"['toast']\">\r\n\t\t\t\t<u-form-item label=\"姓名:\" label-width=\"170\" prop=\"cardno\">\r\n\t\t\t\t\t<u-input v-model=\"form.name\" placeholder=\"请输入姓名\"/>\r\n\t\t\t\t</u-form-item>\r\n\t\t\t\t<u-form-item label=\"身份证号:\" label-width=\"170\" prop=\"cardno\">\r\n\t\t\t\t\t<u-input v-model=\"form.cardno\" placeholder=\"请输入身份证号\"/>\r\n\t\t\t\t</u-form-item>\r\n\t\t\t\t<u-form-item label=\"身份证正反面:\" label-width=\"200\">\r\n\t\t\t\t\t<u-upload upload-text=\"选择或拍照\" width=\"180\" height=\"180\" ref=\"uUpload\" :action=\"action\"  :fileList=\"fileList\" :form-data=\"form_data\" ></u-upload> \r\n\t\t\t\t</u-form-item>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"submit_con\">\r\n\t\t\t\t\t<u-button type=\"primary\" @click=\"submit\">提交</u-button>\r\n\t\t\t\t</view>\r\n\t\t\t</u-form>\n\t</view>\n</template>\n\n<script>\r\n\timport {toast, clearStorageSync, setStorageSync,getStorageSync, useRouter} from '@/utils/utils.js'\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\taction: this.$api_url + '/api/index/upload_cos',\r\n\t\t\t\tfileList:[],\r\n\t\t\t\tform: {\r\n\t\t\t\t\tcardno: '',\r\n\t\t\t\t\tphoto:'',\r\n\t\t\t\t\tname:''\r\n\t\t\t\t},\r\n\t\t\t\t\r\n\t\t\t\tform_data : {\r\n\t\t\t\t\tfloder : 'photo'\r\n\t\t\t\t},\r\n\t\t\t\trules: {\r\n\t\t\t\t\tcardno: [\r\n\t\t\t\t\t\t{ \r\n\t\t\t\t\t\t\trequired: true, \r\n\t\t\t\t\t\t\tmessage: '请输入姓名', \r\n\t\t\t\t\t\t\t// 可以单个或者同时写两个触发验证方式 \r\n\t\t\t\t\t\t\ttrigger: ['change','blur'],\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t],\r\n\t\t\t\t\tcardno: [\r\n\t\t\t\t\t\t{ \r\n\t\t\t\t\t\t\trequired: true, \r\n\t\t\t\t\t\t\tmessage: '请输入身份证号码', \r\n\t\t\t\t\t\t\t// 可以单个或者同时写两个触发验证方式 \r\n\t\t\t\t\t\t\ttrigger: ['change','blur'],\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t]\r\n\r\n\t\t\t\t}\n\t\t\t};\n\t\t},\r\n\t\tonReady(){\r\n\t\t\tthis.$refs.uForm.setRules(this.rules);\r\n\t\t},\r\n\t\t\r\n\t\tmethods: {\r\n\t\t\tsubmit() {\r\n\t\t\t\tthis.$refs.uForm.validate(valid => {\r\n\t\t\t\t\tif (valid) {\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t//获取上传列表\r\n\t\t\t\t\t\tlet _uploadPhoto_data = {}\r\n\t\t\t\t\t\tvar _list = this.$refs.uUpload.lists\r\n\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\tfor (let i = 0; i < _list.length; i++) {\r\n\t\t\t\t\t\t\t _uploadPhoto_data[i] = {\r\n\t\t\t\t\t\t\t \turl: _list[i].response.data.file\r\n\t\t\t\t\t\t\t }\r\n\t\t\t\t\t\t} \r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tthis.form.photo = JSON.stringify(_uploadPhoto_data),\r\n\t\t\t\t\t\tthis.$api.auth(this.form).then(res => {\r\n\t\t\t\t\t\t\tif(res.code==0){\r\n\t\t\t\t\t\t\t\ttoast(res.msg)\r\n\t\t\t\t\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\t\t\t\t\t uni.$emit('refresh', {})  //添加事件\r\n\t\t\t\t\t\t\t\t\t uni.navigateBack()\r\n\t\t\t\t\t\t\t\t},500)\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\tconsole.log(res); \r\n\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\ttoast(res.msg)\r\n\t\t\t\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\t\r\n\r\n\t\t\t\t\t\tconsole.log(this.form)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tconsole.log('验证失败'); \r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t\r\n\t\t\t}\r\n\t\t},\n\t}\n</script>\n\n<style lang=\"scss\">\n.content{padding:30rpx}\n</style>\n\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./auth.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./auth.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753690216412\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}