{"version": 3, "sources": ["webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-image/u-image.vue?b5ca", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-image/u-image.vue?a2d2", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-image/u-image.vue?1390", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-image/u-image.vue?7bec", "uni-app:///uni_modules/uview-ui/components/u-image/u-image.vue", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-image/u-image.vue?28d7", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/uview-ui/components/u-image/u-image.vue?8386"], "names": ["name", "props", "src", "type", "default", "mode", "width", "height", "shape", "borderRadius", "lazyLoad", "showMenuByLongpress", "loadingIcon", "errorIcon", "showLoading", "showError", "fade", "webp", "duration", "bgColor", "data", "isError", "loading", "opacity", "durationTime", "backgroundStyle", "watch", "immediate", "handler", "computed", "wrapStyle", "style", "methods", "onClick", "onError<PERSON><PERSON><PERSON>", "onLoadHandler", "setTimeout", "removeBgColor", "backgroundColor"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2OAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzDA;AAAA;AAAA;AAAA;AAAimB,CAAgB,2nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACwCrnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAzBA,eA0BA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;IACA;IACAa;MACAd;MACAC;IACA;IACA;IACAc;MACAf;MACAC;IACA;IACA;IACAe;MACAhB;MACAC;IACA;EACA;EACAgB;IACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;IACA;EACA;EACAC;IACAxB;MACAyB;MACAC;QACA;UACA;UACA;UACA;QACA;UACA;UACA;QACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;MACAC;MACAA;MACA;MACAA;MACA;MACAA;MACA;QACAA;QACAA;MACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAC;QACA;QACA;QACAA;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC7OA;AAAA;AAAA;AAAA;AAAoqC,CAAgB,0oCAAG,EAAC,C;;;;;;;;;;;ACAxrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-image/u-image.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-image.vue?vue&type=template&id=042b391e&scoped=true&\"\nvar renderjs\nimport script from \"./u-image.vue?vue&type=script&lang=js&\"\nexport * from \"./u-image.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-image.vue?vue&type=style&index=0&id=042b391e&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"042b391e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-image/u-image.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-image.vue?vue&type=template&id=042b391e&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.wrapStyle, _vm.backgroundStyle])\n  var g0 =\n    !_vm.isError && !(_vm.shape == \"circle\")\n      ? _vm.$u.addUnit(_vm.borderRadius)\n      : null\n  var g1 =\n    _vm.showLoading && _vm.loading && !(_vm.shape == \"circle\")\n      ? _vm.$u.addUnit(_vm.borderRadius)\n      : null\n  var g2 =\n    _vm.showError && _vm.isError && !_vm.loading && !(_vm.shape == \"circle\")\n      ? _vm.$u.addUnit(_vm.borderRadius)\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        g0: g0,\n        g1: g1,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-image.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-image.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"u-image\" @tap=\"onClick\" :style=\"[wrapStyle, backgroundStyle]\">\n\t\t<image\n\t\t\tv-if=\"!isError\"\n\t\t\t:src=\"src\"\n\t\t\t:mode=\"mode\"\n\t\t\t@error=\"onErrorHandler\"\n\t\t\t@load=\"onLoadHandler\"\n\t\t\t:lazy-load=\"lazyLoad\"\n\t\t\tclass=\"u-image__image\"\n\t\t\t:show-menu-by-longpress=\"showMenuByLongpress\"\n\t\t\t:style=\"{\n\t\t\t\tborderRadius: shape == 'circle' ? '50%' : $u.addUnit(borderRadius)\n\t\t\t}\"\n\t\t></image>\n\t\t<view\n\t\t\tv-if=\"showLoading && loading\"\n\t\t\tclass=\"u-image__loading\"\n\t\t\t:style=\"{\n\t\t\t\tborderRadius: shape == 'circle' ? '50%' : $u.addUnit(borderRadius),\n\t\t\t\tbackgroundColor: bgColor\n\t\t\t}\"\n\t\t>\n\t\t\t<slot v-if=\"$slots.loading\" name=\"loading\" />\n\t\t\t<u-icon v-else :name=\"loadingIcon\" :width=\"width\" :height=\"height\"></u-icon>\n\t\t</view>\n\t\t<view\n\t\t\tv-if=\"showError && isError && !loading\"\n\t\t\tclass=\"u-image__error\"\n\t\t\t:style=\"{\n\t\t\t\tborderRadius: shape == 'circle' ? '50%' : $u.addUnit(borderRadius)\n\t\t\t}\"\n\t\t>\n\t\t\t<slot v-if=\"$slots.error\" name=\"error\" />\n\t\t\t<u-icon v-else :name=\"errorIcon\" :width=\"width\" :height=\"height\"></u-icon>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n/**\n * Image 图片\n * @description 此组件为uni-app的image组件的加强版，在继承了原有功能外，还支持淡入动画、加载中、加载失败提示、圆角值和形状等。\n * @tutorial https://uviewui.com/components/image.html\n * @property {String} src 图片地址\n * @property {String} mode 裁剪模式，见官网说明\n * @property {String | Number} width 宽度，单位任意，如果为数值，则为rpx单位（默认100%）\n * @property {String | Number} height 高度，单位任意，如果为数值，则为rpx单位（默认 auto）\n * @property {String} shape 图片形状，circle-圆形，square-方形（默认square）\n * @property {String | Number} border-radius 圆角值，单位任意，如果为数值，则为rpx单位（默认 0）\n * @property {Boolean} lazy-load 是否懒加载，仅微信小程序、App、百度小程序、字节跳动小程序有效（默认 true）\n * @property {Boolean} show-menu-by-longpress 是否开启长按图片显示识别小程序码菜单，仅微信小程序有效（默认 false）\n * @property {String} loading-icon 加载中的图标，或者小图片（默认 photo）\n * @property {String} error-icon 加载失败的图标，或者小图片（默认 error-circle）\n * @property {Boolean} show-loading 是否显示加载中的图标或者自定义的slot（默认 true）\n * @property {Boolean} show-error 是否显示加载错误的图标或者自定义的slot（默认 true）\n * @property {Boolean} fade 是否需要淡入效果（默认 true）\n * @property {String Number} width 传入图片路径时图片的宽度\n * @property {String Number} height 传入图片路径时图片的高度\n * @property {Boolean} webp 只支持网络资源，只对微信小程序有效（默认 false）\n * @property {String | Number} duration 搭配fade参数的过渡时间，单位ms（默认 500）\n * @event {Function} click 点击图片时触发\n * @event {Function} error 图片加载失败时触发\n * @event {Function} load 图片加载成功时触发\n * @example <u-image width=\"100%\" height=\"300rpx\" :src=\"src\"></u-image>\n */\nexport default {\n\tname: 'u-image',\n\tprops: {\n\t\t// 图片地址\n\t\tsrc: {\n\t\t\ttype: String,\n\t\t\tdefault: ''\n\t\t},\n\t\t// 裁剪模式\n\t\tmode: {\n\t\t\ttype: String,\n\t\t\tdefault: 'aspectFill'\n\t\t},\n\t\t// 宽度，单位任意\n\t\twidth: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: '100%'\n\t\t},\n\t\t// 高度，单位任意\n\t\theight: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: 'auto'\n\t\t},\n\t\t// 图片形状，circle-圆形，square-方形\n\t\tshape: {\n\t\t\ttype: String,\n\t\t\tdefault: 'square'\n\t\t},\n\t\t// 圆角，单位任意\n\t\tborderRadius: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: 0\n\t\t},\n\t\t// 是否懒加载，微信小程序、App、百度小程序、字节跳动小程序\n\t\tlazyLoad: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t},\n\t\t// 开启长按图片显示识别微信小程序码菜单\n\t\tshowMenuByLongpress: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t},\n\t\t// 加载中的图标，或者小图片\n\t\tloadingIcon: {\n\t\t\ttype: String,\n\t\t\tdefault: 'photo'\n\t\t},\n\t\t// 加载失败的图标，或者小图片\n\t\terrorIcon: {\n\t\t\ttype: String,\n\t\t\tdefault: 'error-circle'\n\t\t},\n\t\t// 是否显示加载中的图标或者自定义的slot\n\t\tshowLoading: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t},\n\t\t// 是否显示加载错误的图标或者自定义的slot\n\t\tshowError: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t},\n\t\t// 是否需要淡入效果\n\t\tfade: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t},\n\t\t// 只支持网络资源，只对微信小程序有效\n\t\twebp: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t},\n\t\t// 过渡时间，单位ms\n\t\tduration: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: 500\n\t\t},\n\t\t// 背景颜色，用于深色页面加载图片时，为了和背景色融合\n\t\tbgColor: {\n\t\t\ttype: String,\n\t\t\tdefault: '#f3f4f6'\n\t\t}\n\t},\n\tdata() {\n\t\treturn {\n\t\t\t// 图片是否加载错误，如果是，则显示错误占位图\n\t\t\tisError: false,\n\t\t\t// 初始化组件时，默认为加载中状态\n\t\t\tloading: true,\n\t\t\t// 不透明度，为了实现淡入淡出的效果\n\t\t\topacity: 1,\n\t\t\t// 过渡时间，因为props的值无法修改，故需要一个中间值\n\t\t\tdurationTime: this.duration,\n\t\t\t// 图片加载完成时，去掉背景颜色，因为如果是png图片，就会显示灰色的背景\n\t\t\tbackgroundStyle: {}\n\t\t};\n\t},\n\twatch: {\n\t\tsrc: {\n\t\t\timmediate: true,\n\t\t\thandler (n) {\n\t\t\t\tif(!n) {\n\t\t\t\t\t// 如果传入null或者''，或者false，或者undefined，标记为错误状态\n\t\t\t\t\tthis.isError = true;\n\t\t\t\t\tthis.loading = false;\n\t\t\t\t} else {\n\t\t\t\t\tthis.isError = false;\n\t\t\t\t\tthis.loading = true;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t},\n\tcomputed: {\n\t\twrapStyle() {\n\t\t\tlet style = {};\n\t\t\t// 通过调用addUnit()方法，如果有单位，如百分比，px单位等，直接返回，如果是纯粹的数值，则加上rpx单位\n\t\t\tstyle.width = this.$u.addUnit(this.width);\n\t\t\tstyle.height = this.$u.addUnit(this.height);\n\t\t\t// 如果是配置了圆形，设置50%的圆角，否则按照默认的配置值\n\t\t\tstyle.borderRadius = this.shape == 'circle' ? '50%' : this.$u.addUnit(this.borderRadius);\n\t\t\t// 如果设置圆角，必须要有hidden，否则可能圆角无效\n\t\t\tstyle.overflow = this.borderRadius > 0 ? 'hidden' : 'visible';\n\t\t\tif (this.fade) {\n\t\t\t\tstyle.opacity = this.opacity;\n\t\t\t\tstyle.transition = `opacity ${Number(this.durationTime) / 1000}s ease-in-out`;\n\t\t\t}\n\t\t\treturn style;\n\t\t}\n\t},\n\tmethods: {\n\t\t// 点击图片\n\t\tonClick() {\n\t\t\tthis.$emit('click');\n\t\t},\n\t\t// 图片加载失败\n\t\tonErrorHandler(err) {\n\t\t\tthis.loading = false;\n\t\t\tthis.isError = true;\n\t\t\tthis.$emit('error', err);\n\t\t},\n\t\t// 图片加载完成，标记loading结束\n\t\tonLoadHandler() {\n\t\t\tthis.loading = false;\n\t\t\tthis.isError = false;\n\t\t\tthis.$emit('load');\n\t\t\t// 如果不需要动画效果，就不执行下方代码，同时移除加载时的背景颜色\n\t\t\t// 否则无需fade效果时，png图片依然能看到下方的背景色\n\t\t\tif (!this.fade) return this.removeBgColor();\n\t\t\t// 原来opacity为1(不透明，是为了显示占位图)，改成0(透明，意味着该元素显示的是背景颜色，默认的灰色)，再改成1，是为了获得过渡效果\n\t\t\tthis.opacity = 0;\n\t\t\t// 这里设置为0，是为了图片展示到背景全透明这个过程时间为0，延时之后延时之后重新设置为duration，是为了获得背景透明(灰色)\n\t\t\t// 到图片展示的过程中的淡入效果\n\t\t\tthis.durationTime = 0;\n\t\t\t// 延时50ms，否则在浏览器H5，过渡效果无效\n\t\t\tsetTimeout(() => {\n\t\t\t\tthis.durationTime = this.duration;\n\t\t\t\tthis.opacity = 1;\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.removeBgColor();\n\t\t\t\t}, this.durationTime);\n\t\t\t}, 50);\n\t\t},\n\t\t// 移除图片的背景色\n\t\tremoveBgColor() {\n\t\t\t// 淡入动画过渡完成后，将背景设置为透明色，否则png图片会看到灰色的背景\n\t\t\tthis.backgroundStyle = {\n\t\t\t\tbackgroundColor: 'transparent'\n\t\t\t};\n\t\t}\n\t}\n};\n</script>\n\n<style scoped lang=\"scss\">\n@import '../../libs/css/style.components.scss';\n\n.u-image {\n\tposition: relative;\n\ttransition: opacity 0.5s ease-in-out;\n\n\t&__image {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n\n\t&__loading,\n\t&__error {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\t@include vue-flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbackground-color: $u-bg-color;\n\t\tcolor: $u-tips-color;\n\t\tfont-size: 46rpx;\n\t}\n}\n</style>\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-image.vue?vue&type=style&index=0&id=042b391e&scoped=true&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-image.vue?vue&type=style&index=0&id=042b391e&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753690217175\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}