<view class="content"><u-form class="vue-ref" vue-id="00418276-1" data-ref="uForm" bind:__l="__l" vue-slots="{{['default']}}"><u-form-item vue-id="{{('00418276-2')+','+('00418276-1')}}" label="反馈内容:" label-width="150" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('00418276-3')+','+('00418276-2')}}" type="textarea" value="{{content}}" data-event-opts="{{[['^input',[['__set_model',['','content','$event',[]]]]]]}}" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('00418276-4')+','+('00418276-1')}}" label="上传图片:" label-width="150" bind:__l="__l" vue-slots="{{['default']}}"><u-upload class="vue-ref" vue-id="{{('00418276-5')+','+('00418276-4')}}" upload-text="选择或拍照" width="180" height="180" action="{{action}}" fileList="{{fileList}}" form-data="{{form_data}}" data-ref="uUpload" bind:__l="__l"></u-upload></u-form-item><u-button vue-id="{{('00418276-6')+','+('00418276-1')}}" type="primary" data-event-opts="{{[['^click',[['submit']]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}">确定</u-button></u-form></view>