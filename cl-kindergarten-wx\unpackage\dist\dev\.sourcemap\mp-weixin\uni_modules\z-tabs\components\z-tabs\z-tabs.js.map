{"version": 3, "sources": ["webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/z-tabs/components/z-tabs/z-tabs.vue?df54", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/z-tabs/components/z-tabs/z-tabs.vue?3742", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/z-tabs/components/z-tabs/z-tabs.vue?302c", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/z-tabs/components/z-tabs/z-tabs.vue?9b2b", "uni-app:///uni_modules/z-tabs/components/z-tabs/z-tabs.vue", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/z-tabs/components/z-tabs/z-tabs.vue?338f", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/uni_modules/z-tabs/components/z-tabs/z-tabs.vue?270b"], "names": ["config", "name", "data", "currentIndex", "currentSwiperIndex", "bottomDotX", "bottomDotXForIndex", "showBottomDot", "shouldSetDx", "barCal<PERSON><PERSON><PERSON><PERSON>", "px<PERSON><PERSON><PERSON><PERSON><PERSON>", "scrollLeft", "tabsSuper<PERSON><PERSON><PERSON>", "tabsWidth", "tabsHeight", "tabsLeft", "tabsContainerWidth", "itemNodeInfos", "isFirstLoaded", "currentScrollLeft", "changeTriggerFailed", "currentChanged", "props", "list", "type", "default", "current", "scrollCount", "tabsStyle", "tabWidth", "<PERSON><PERSON><PERSON><PERSON>", "barHeight", "barStyle", "bottomSpace", "barAnimateMode", "<PERSON><PERSON><PERSON>", "valueKey", "activeColor", "inactiveColor", "disabledColor", "activeStyle", "inactiveStyle", "disabledStyle", "bgColor", "badgeMaxCount", "badgeStyle", "initTriggerChange", "mounted", "watch", "handler", "immediate", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "computed", "shouldScroll", "finalTabsHeight", "tabStyle", "stl", "tabsListStyle", "showAnimate", "dotTransition", "finalDotStyle", "width", "height", "opacity", "finalTabWidth", "finalBarHeight", "finalBottomSpace", "methods", "setDx", "dxRate", "nextIndex", "unlockDx", "updateSubviewLayout", "setTimeout", "tryCount", "tabsClick", "scroll", "_lockDx", "_preUpdateDotPosition", "uni", "scrollOffset", "_updateDotPosition", "node", "offset", "JSON", "nodeRes", "oldNode", "_handleListChange", "delayTime", "i", "_getBottomDotX", "_getNodeClientRect", "res", "resolve", "_formatCount", "_convertTextToPx", "text", "isRpx"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA2I;AAC3I;AAC0D;AACL;AACqC;;;AAG1F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,yGAAM;AACR,EAAE,kHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtEA;AAAA;AAAA;AAAA;AAAgmB,CAAgB,0nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACiDpnB;AAAA;AAAA;AAEA;AACA;EACA;EACA;IACAA;EACA;IACA;EACA;EACA;EACA;AACA;AACA;AACA;EACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA5BA,gBA6BA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;QACA;MACA;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;QACA;MACA;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;IACA;IACAa;MACAd;MACAC;IACA;IACA;IACAc;MACAf;MACAC;IACA;IACA;IACAe;MACAhB;MACAC;QACA;MACA;IACA;IACA;IACAgB;MACAjB;MACAC;QACA;MACA;IACA;IACA;IACAiB;MACAlB;MACAC;QACA;MACA;IACA;IACA;IACAkB;MACAnB;MACAC;IACA;IACA;IACAmB;MACApB;MACAC;IACA;IACA;IACAoB;MACArB;MACAC;QACA;MACA;IACA;IACA;IACAqB;MACAtB;MACAC;IACA;EACA;EACAsB;IACA;EACA;EACAC;IACAtB;MACAuB;QACA;QACA;QACA;QACA;UACA;YACA;UACA;YACA;UACA;QACA;QACA;MACA;MACAC;IACA;IACA3B;MACA0B;QACA;MACA;MACAC;IACA;IACA7C;MACA;QAEA;QAEA,4BAaA;MACA;IACA;IACA8C;MACAF;QACA;QACA;MACA;MACAC;IACA;IACA/C;MACA8C;QACA;MACA;MACAC;IACA;EACA;EACAE;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;QAAA;MAAA;QAAA;MAAA;MACA;QACAC;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;QAAA;MAAA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;QAAAC;QAAAC;QAAAC;MAAA;IACA;IACAC;MACA;IACA;IACAb;MACA;IACA;IACAc;MACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAC;QACA;QACA;UACA;QACA;MACA;MACA;MACA;MACAC;MACAA;MACA;MACA;MACA;MACA;QACA;MACA;QACA;QACA;QACA;QACA;UACA;YACA;YACA;YACA7D;UACA;QACA;UACA;YACA;YACAA;YACA;UACA;YACA;UACA;QACA;QACAA;QACA;MACA;IACA;IACA;IACA8D;MAAA;MACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MAAA;MACA;QACA;QAIAC;UACA;YACA;cACA;gBACAA;kBACAC;kBACA;gBACA;gBACA;cACA;cACA;cACA;cACA;cACA;YACA;UACA;UACA;YACA;cACA;YACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;QACA;QACA;QACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MAAA;MAEA;QACAC;UACAC;QACA;UACA;YACA;YACA;UACA;YACA;UACA;QACA;MACA;IAMA;IACA;IACAC;MAAA;MACA;MACA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBACAC;gBACAnE;gBAAA,MACAoE;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAC;gBACA;kBACAH;kBACAC;kBACA;kBACAnE;kBACA;oBACAsE;oBACAtE;kBACA;gBACA;cAAA;gBAEA;gBACA;gBACA;kBACAyD;oBACA;oBACA9D;oBACA;sBACAA;oBACA;oBACA;sBACA;oBACA;oBACA;sBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CACA;IACA;IACA;IACA4E;MAAA;MACA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACAtE;kBACAD;kBACAwE;kBAIAf;oBAAA;oBAAA;sBAAA;wBAAA;0BAAA;4BACAgB;0BAAA;4BAAA;8BAAA;8BAAA;4BAAA;4BAAA;4BAAA,OACA;0BAAA;4BAAAJ;4BACA;8BACAH;8BACAA;8BACAjE;8BACAD;4BACA;4BACA;8BACA;8BACA;8BACA;4BACA;0BAAA;4BAZAyE;4BAAA;4BAAA;0BAAA;4BAcA;4BACA;4BACA;0BAAA;0BAAA;4BAAA;wBAAA;sBAAA;oBAAA;kBAAA,CACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CACA;MAEA;QACA;UACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MAAA;MACA;IACA;IACA;IACAC;MAAA;MAeA;MACAC;MACA;QACAA;UACAC;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;MACA;MACA;MACA;QACAC;QACAC;MACA;QACAD;MACA;QACAA;MACA;MACA;QACA;QACA;MACA;MACA;IACA;EACA;AAEA;AAAA,4B;;;;;;;;;;;;;ACrmBA;AAAA;AAAA;AAAA;AAA04B,CAAgB,24BAAG,EAAC,C;;;;;;;;;;;ACA95B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/z-tabs/components/z-tabs/z-tabs.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./z-tabs.vue?vue&type=template&id=320c567f&scoped=true&name=z-tabs&\"\nvar renderjs\nimport script from \"./z-tabs.vue?vue&type=script&lang=js&\"\nexport * from \"./z-tabs.vue?vue&type=script&lang=js&\"\nimport style0 from \"./z-tabs.vue?vue&type=style&index=0&id=320c567f&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"320c567f\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/z-tabs/components/z-tabs/z-tabs.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./z-tabs.vue?vue&type=template&id=320c567f&scoped=true&name=z-tabs&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([\n    {\n      background: _vm.bgColor,\n    },\n    _vm.tabsStyle,\n  ])\n  var s1 = _vm.__get_style([_vm.tabsListStyle])\n  var s2 = _vm.__get_style([\n    _vm.tabsListStyle,\n    {\n      marginTop: -_vm.finalBottomSpace + \"px\",\n    },\n  ])\n  var s3 = _vm.__get_style([_vm.tabStyle])\n  var l0 = _vm.__map(_vm.list, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var s4 = _vm.__get_style([\n      {\n        color: item.disabled\n          ? _vm.disabledColor\n          : _vm.currentIndex === index\n          ? _vm.activeColor\n          : _vm.inactiveColor,\n      },\n      item.disabled\n        ? _vm.disabledStyle\n        : _vm.currentIndex === index\n        ? _vm.activeStyle\n        : _vm.inactiveStyle,\n    ])\n    var g0 = item.badge && _vm._formatCount(item.badge.count).length\n    var s5 = g0 ? _vm.__get_style([_vm.badgeStyle]) : null\n    var m0 = g0 ? _vm._formatCount(item.badge.count) : null\n    return {\n      $orig: $orig,\n      s4: s4,\n      g0: g0,\n      s5: s5,\n      m0: m0,\n    }\n  })\n  var s6 = _vm.__get_style([\n    {\n      transform: \"translateX(\" + _vm.bottomDotX + \"px)\",\n      transition: _vm.dotTransition,\n      background: _vm.activeColor,\n    },\n    _vm.finalDotStyle,\n  ])\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1,\n        s2: s2,\n        s3: s3,\n        l0: l0,\n        s6: s6,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./z-tabs.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./z-tabs.vue?vue&type=script&lang=js&\"", "<!-- z-tabs v0.2.5 by-ZXLee -->\n<!-- github地址:https://github.com/SmileZXLee/uni-z-tabs -->\n<!-- dcloud地址:https://ext.dcloud.net.cn/plugin?name=z-tabs -->\n<!-- 反馈QQ群：790460711 -->\n\n<template name=\"z-tabs\">\n\t<view class=\"z-tabs-conatiner\" :style=\"[{background:bgColor}, tabsStyle]\">\n\t\t<view class=\"z-tabs-left\">\n\t\t\t<slot name=\"left\" />\n\t\t</view>\n\t\t<view ref=\"z-tabs-scroll-view-conatiner\" class=\"z-tabs-scroll-view-conatiner\">\n\t\t\t<scroll-view ref=\"z-tabs-scroll-view\" class=\"z-tabs-scroll-view\" :scroll-x=\"true\" :scroll-left=\"scrollLeft\" :show-scrollbar=\"false\" :scroll-with-animation=\"isFirstLoaded\" @scroll=\"scroll\">\n\t\t\t\t<view class=\"z-tabs-list-container\" :style=\"[tabsListStyle]\">\n\t\t\t\t\t<view class=\"z-tabs-list\" :style=\"[tabsListStyle, {marginTop: -finalBottomSpace+'px'}]\">\n\t\t\t\t\t\t<view :ref=\"`z-tabs-item-${index}`\" :id=\"`z-tabs-item-${index}`\" class=\"z-tabs-item\" :style=\"[tabStyle]\" v-for=\"(item,index) in list\" :key=\"index\" @click=\"tabsClick(index,item)\">\n\t\t\t\t\t\t\t<view class=\"z-tabs-item-title-container\">\n\t\t\t\t\t\t\t\t<text :class=\"{'z-tabs-item-title':true,'z-tabs-item-title-disabled':item.disabled}\" \n\t\t\t\t\t\t\t\t\t:style=\"[{color:item.disabled?disabledColor:(currentIndex===index?activeColor:inactiveColor)},item.disabled?disabledStyle:(currentIndex===index?activeStyle:inactiveStyle)]\">\n\t\t\t\t\t\t\t\t\t{{item[nameKey]||item}}\n\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t\t<text v-if=\"item.badge&&_formatCount(item.badge.count).length\" class=\"z-tabs-item-badge\" :style=\"[badgeStyle]\">{{_formatCount(item.badge.count)}}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"z-tabs-bottom\" :style=\"[{width: tabsContainerWidth+'px', bottom: finalBottomSpace+'px'}]\">\n\t\t\t\t\t\t<view ref=\"z-tabs-bottom-dot\" class=\"z-tabs-bottom-dot\"\n\t\t\t\t\t\t<!-- #ifndef APP-NVUE -->\n\t\t\t\t\t\t:style=\"[{transform:`translateX(${bottomDotX}px)`,transition:dotTransition,background:activeColor},finalDotStyle]\"\n\t\t\t\t\t\t<!-- #endif -->\n\t\t\t\t\t\t<!-- #ifdef APP-NVUE -->\n\t\t\t\t\t\t:style=\"[{background:activeColor},finalDotStyle]\"\n\t\t\t\t\t\t<!-- #endif -->\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\t\n\t\t\t\t</view>\n\t\t\t</scroll-view>\n\t\t</view>\n\t\t<view class=\"z-tabs-right\">\n\t\t\t<slot name=\"right\" />\n\t\t</view>\n\t\t\n\t</view>\n</template>\n\n<script>\n\t// #ifdef APP-NVUE\n\tconst weexDom = weex.requireModule('dom');\n\tconst weexAnimation = weex.requireModule('animation');\n\t// #endif\n\timport zTabsConfig from './config/index'\n\t\n\t//获取默认配置信息\n\tfunction _gc(key, defaultValue) {\n\t\tlet config = null;\n\t\tif (zTabsConfig && Object.keys(zTabsConfig).length) {\n\t\t\tconfig = zTabsConfig;\n\t\t} else {\n\t\t\treturn defaultValue;\n\t\t}\n\t\tconst value = config[_toKebab(key)];\n\t\treturn value === undefined ? defaultValue : value;\n\t}\n\t//驼峰转短横线\n\tfunction _toKebab(value) {\n\t\treturn value.replace(/([A-Z])/g, \"-$1\").toLowerCase();\n\t}\n\t\n\t/**\n\t * z-tabs 标签\n\t * @description 一个简单轻量的tabs标签，全平台兼容，支持nvue、vue3\n\t * @tutorial https://ext.dcloud.net.cn/plugin?name=z-tabs\n\t * @property {Array} list 数据源数组，支持形如['tab1','tab2']的格式或[{name:'tab1',value:1}]的格式\n\t * @property {Number|String} current 当前选中的index，默认为0\n\t * @property {Number|String} scroll-count list数组长度超过scrollCount时滚动显示(不自动铺满全屏)，默认为5\n\t * @property {Number|String} tab-width 自定义每个tab的宽度，默认为0，即代表根据内容自动撑开，单位rpx，支持传100、\"100px\"或\"100rpx\"\n\t * @property {Number|String} bar-width 滑块宽度，单位rpx，支持传100、\"100px\"或\"100rpx\"\n\t * @property {Number|String} bar-height 滑块高度，单位rpx，支持传100、\"100px\"或\"100rpx\"\n\t * @property {Object} bar-style 滑块样式，其中的width和height将被bar-width和bar-height覆盖\n\t * @property {Number|String} bottom-space tabs与底部的间距，单位rpx，支持传100、\"100px\"或\"100rpx\"\n\t * @property {String} bar-animate-mode 切换tab时滑块动画模式，与swiper联动时有效，点击切换tab时无效，必须调用setDx。默认为line，即切换tab时滑块宽度保持不变，线性运动。可选值为worm，即为类似毛毛虫蠕动效果\n\t * @property {String} name-key list中item的name(标题)的key，默认为name\n\t * @property {String} value-key list中item的value的key，默认为value\n\t * @property {String} active-color 激活状态tab的颜色\n\t * @property {String} inactive-color 未激活状态tab的颜色\n\t * @property {String} disabled-color 禁用状态tab的颜色\n\t * @property {Object} active-style 激活状态tab的样式\n\t * @property {Object} inactive-style 未激活状态tab的样式\n\t * @property {Object} disabled-style 禁用状态tab的样式\n\t * @property {Number|String} badge-max-count 徽标数最大数字限制，超过这个数字将变成badge-max-count+，默认为99\n\t * @property {Object} badge-style 徽标样式，例如可自定义背景色，字体等等\n\t * @property {String} bg-color z-tabs背景色\n\t * @property {Object} tabs-style z-tabs样式\n\t * @property {Boolean} init-trigger-change 初始化时是否自动触发change事件\n\t * @event {Function(index,value)} change tabs改变时触发，index:当前切换到的index；value:当前切换到的value\n\t * @example <z-tabs :list=\"list\"></z-tabs>\n\t */\n\texport default {\n\t\tname: 'z-tabs',\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tcurrentIndex: 0,\n\t\t\t\tcurrentSwiperIndex: 0,\n\t\t\t\tbottomDotX: -1,\n\t\t\t\tbottomDotXForIndex: 0,\n\t\t\t\tshowBottomDot: false,\n\t\t\t\tshouldSetDx: true,\n\t\t\t\t\n\t\t\t\tbarCalcedWidth: 0,\n\t\t\t\tpxBarWidth: 0,\n\t\t\t\tscrollLeft: 0,\n\t\t\t\ttabsSuperWidth: uni.upx2px(750),\n\t\t\t\ttabsWidth: uni.upx2px(750),\n\t\t\t\ttabsHeight: uni.upx2px(80),\n\t\t\t\ttabsLeft: 0,\n\t\t\t\ttabsContainerWidth: 0,\n\t\t\t\titemNodeInfos: [],\n\t\t\t\tisFirstLoaded: false,\n\t\t\t\tcurrentScrollLeft: 0,\n\t\t\t\tchangeTriggerFailed: false,\n\t\t\t\tcurrentChanged: false\n\t\t\t};\n\t\t},\n\t\tprops: {\n\t\t\t//数据源数组，支持形如['tab1','tab2']的格式或[{name:'tab1',value:1}]的格式\n\t\t\tlist: {\n\t\t\t\ttype: Array,\n\t\t\t\tdefault: function() {\n\t\t\t\t\treturn [];\n\t\t\t\t}\n\t\t\t},\n\t\t\t//当前选中的index\n\t\t\tcurrent: {\n\t\t\t\ttype: [Number, String],\n\t\t\t\tdefault: _gc('current',0)\n\t\t\t},\n\t\t\t//list数组长度超过scrollCount时滚动显示(不自动铺满全屏)\n\t\t\tscrollCount: {\n\t\t\t\ttype: [Number, String],\n\t\t\t\tdefault: _gc('scrollCount',5)\n\t\t\t},\n\t\t\t//z-tabs样式\n\t\t\ttabsStyle: {\n\t\t\t\ttype: Object,\n\t\t\t\tdefault: function() {\n\t\t\t\t\treturn _gc('tabsStyle',{})\n\t\t\t\t}\n\t\t\t},\n\t\t\t//自定义每个tab的宽度，默认为0，即代表根据内容自动撑开，单位rpx，支持传100、\"100px\"或\"100rpx\"\n\t\t\ttabWidth: {\n\t\t\t\ttype: [Number, String],\n\t\t\t\tdefault: _gc('tabWidth',0)\n\t\t\t},\n\t\t\t//滑块宽度，单位rpx，支持传100、\"100px\"或\"100rpx\"\n\t\t\tbarWidth: {\n\t\t\t\ttype: [Number, String],\n\t\t\t\tdefault: _gc('barWidth',45)\n\t\t\t},\n\t\t\t//滑块高度，单位rpx，支持传100、\"100px\"或\"100rpx\"\n\t\t\tbarHeight: {\n\t\t\t\ttype: [Number, String],\n\t\t\t\tdefault: _gc('barHeight',8)\n\t\t\t},\n\t\t\t//滑块样式，其中的width和height将被barWidth和barHeight覆盖\n\t\t\tbarStyle: {\n\t\t\t\ttype: Object,\n\t\t\t\tdefault: function() {\n\t\t\t\t\treturn _gc('barStyle',{});\n\t\t\t\t}\n\t\t\t},\n\t\t\t//tabs与底部的间距，单位rpx，支持传100、\"100px\"或\"100rpx\"\n\t\t\tbottomSpace: {\n\t\t\t\ttype: [Number, String],\n\t\t\t\tdefault: _gc('bottomSpace',8)\n\t\t\t},\n\t\t\t//切换tab时滑块动画模式，与swiper联动时有效，点击切换tab时无效，必须调用setDx。默认为line，即切换tab时滑块宽度保持不变，线性运动。可选值为worm，即为类似毛毛虫蠕动效果\n\t\t\tbarAnimateMode: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: _gc('barAnimateMode','line')\n\t\t\t},\n\t\t\t//list中item的name(标题)的key\n\t\t\tnameKey: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: _gc('nameKey','name')\n\t\t\t},\n\t\t\t//list中item的value的key\n\t\t\tvalueKey: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: _gc('valueKey','value')\n\t\t\t},\n\t\t\t//激活状态tab的颜色\n\t\t\tactiveColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: _gc('activeColor','#007AFF')\n\t\t\t},\n\t\t\t//未激活状态tab的颜色\n\t\t\tinactiveColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: _gc('inactiveColor','#666666')\n\t\t\t},\n\t\t\t//禁用状态tab的颜色\n\t\t\tdisabledColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: _gc('disabledColor','#bbbbbb')\n\t\t\t},\n\t\t\t//激活状态tab的样式\n\t\t\tactiveStyle: {\n\t\t\t\ttype: Object,\n\t\t\t\tdefault: function() {\n\t\t\t\t\treturn _gc('activeStyle',{});\n\t\t\t\t}\n\t\t\t},\n\t\t\t//未激活状态tab的样式\n\t\t\tinactiveStyle: {\n\t\t\t\ttype: Object,\n\t\t\t\tdefault: function() {\n\t\t\t\t\treturn _gc('inactiveStyle',{});\n\t\t\t\t}\n\t\t\t},\n\t\t\t//禁用状态tab的样式\n\t\t\tdisabledStyle: {\n\t\t\t\ttype: Object,\n\t\t\t\tdefault: function() {\n\t\t\t\t\treturn _gc('disabledStyle',{});\n\t\t\t\t}\n\t\t\t},\n\t\t\t//z-tabs背景色\n\t\t\tbgColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: _gc('bgColor','white')\n\t\t\t},\n\t\t\t//徽标数最大数字限制，超过这个数字将变成badgeMaxCount+\n\t\t\tbadgeMaxCount: {\n\t\t\t\ttype: [Number, String],\n\t\t\t\tdefault: _gc('badgeMaxCount',99)\n\t\t\t},\n\t\t\t//徽标样式，例如可自定义背景色，字体等等\n\t\t\tbadgeStyle: {\n\t\t\t\ttype: Object,\n\t\t\t\tdefault: function() {\n\t\t\t\t\treturn _gc('badgeStyle',{})\n\t\t\t\t}\n\t\t\t},\n\t\t\t//初始化时是否自动触发change事件\n\t\t\tinitTriggerChange: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: _gc('initTriggerChange',false)\n\t\t\t}\n\t\t},\n\t\tmounted() {\n\t\t\tthis.updateSubviewLayout();\n\t\t},\n\t\twatch: {\n\t\t\tcurrent: {\n\t\t\t\thandler(newVal) {\n\t\t\t\t\tthis.currentChanged && this._lockDx();\n\t\t\t\t\tthis.currentIndex = newVal;\n\t\t\t\t\tthis._preUpdateDotPosition(this.currentIndex);\n\t\t\t\t\tif (this.initTriggerChange) {\n\t\t\t\t\t\tif (newVal < this.list.length) {\n\t\t\t\t\t\t\tthis.$emit('change', newVal, this.list[newVal][this.valueKey]);\n\t\t\t\t\t\t}else {\n\t\t\t\t\t\t\tthis.changeTriggerFailed = true;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tthis.currentChanged = true;\n\t\t\t\t},\n\t\t\t\timmediate: true\n\t\t\t},\n\t\t\tlist: {\n\t\t\t\thandler(newVal) {\n\t\t\t\t\tthis._handleListChange(newVal);\n\t\t\t\t},\n\t\t\t\timmediate: false\n\t\t\t},\n\t\t\tbottomDotX(newVal) {\n\t\t\t\tif(newVal >= 0){\n\t\t\t\t\t// #ifndef APP-NVUE\n\t\t\t\t\tthis.showBottomDot = true;\n\t\t\t\t\t// #endif\n\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\t\t\tweexAnimation.transition(this.$refs['z-tabs-bottom-dot'], {\n\t\t\t\t\t\t\tstyles: {\n\t\t\t\t\t\t\t\ttransform: `translateX(${newVal}px)`\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tduration: this.showAnimate ? 200 : 0,\n\t\t\t\t\t\t\tdelay: 0\n\t\t\t\t\t\t})\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tthis.showBottomDot = true;\n\t\t\t\t\t\t},10)\n\t\t\t\t\t\t// #endif\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\t\t\tfinalBarWidth: {\n\t\t\t\thandler(newVal) {\n\t\t\t\t\tthis.barCalcedWidth = newVal;\n\t\t\t\t\tthis.pxBarWidth = this.barCalcedWidth;\n\t\t\t\t},\n\t\t\t\timmediate: true\n\t\t\t},\n\t\t\tcurrentIndex: {\n\t\t\t\thandler(newVal) {\n\t\t\t\t\tthis.currentSwiperIndex = newVal;\n\t\t\t\t},\n\t\t\t\timmediate: true\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\tshouldScroll(){\n\t\t\t\treturn this.list.length > this.scrollCount;\n\t\t\t},\n\t\t\tfinalTabsHeight(){\n\t\t\t\treturn this.tabsHeight;\n\t\t\t},\n\t\t\ttabStyle(){\n\t\t\t\tconst stl = this.shouldScroll ? {'flex-shrink': 0} : {'flex': 1};\n\t\t\t\tif(this.finalTabWidth > 0){\n\t\t\t\t\tstl['width'] = this.finalTabWidth + 'px';\n\t\t\t\t}else{\n\t\t\t\t\tdelete stl.width;\n\t\t\t\t} \n\t\t\t\treturn stl;\n\t\t\t},\n\t\t\ttabsListStyle(){\n\t\t\t\treturn this.shouldScroll ? {} : {'flex':1};\n\t\t\t},\n\t\t\tshowAnimate(){\n\t\t\t\treturn this.isFirstLoaded && !this.shouldSetDx;\n\t\t\t},\n\t\t\tdotTransition(){\n\t\t\t\treturn this.showAnimate ? 'transform .2s linear':'none';\n\t\t\t},\n\t\t\tfinalDotStyle(){\n\t\t\t\treturn {...this.barStyle, width: this.barCalcedWidth + 'px', height: this.finalBarHeight + 'px', opacity: this.showBottomDot ? 1 : 0};\n\t\t\t},\n\t\t\tfinalTabWidth(){\n\t\t\t\treturn this._convertTextToPx(this.tabWidth);\n\t\t\t},\n\t\t\tfinalBarWidth(){\n\t\t\t\treturn this._convertTextToPx(this.barWidth);\n\t\t\t},\n\t\t\tfinalBarHeight(){\n\t\t\t\treturn this._convertTextToPx(this.barHeight);\n\t\t\t},\n\t\t\tfinalBottomSpace(){\n\t\t\t\treturn this._convertTextToPx(this.bottomSpace);\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t//根据swiper的@transition实时更新底部dot位置\n\t\t\tsetDx(dx) {\n\t\t\t\tif (!this.shouldSetDx) return;\n\t\t\t\tconst isLineMode = this.barAnimateMode === 'line';\n\t\t\t\tconst isWormMode = this.barAnimateMode === 'worm';\n\t\t\t\tlet dxRate = dx / this.tabsSuperWidth;\n\t\t\t\tthis.currentSwiperIndex = this.currentIndex + parseInt(dxRate);\n\t\t\t\tconst isRight = dxRate > 0;\n\t\t\t\tconst barWidth = this.pxBarWidth;\n\t\t\t\tif(this.currentSwiperIndex !== this.currentIndex){\n\t\t\t\t\tdxRate = dxRate - (this.currentSwiperIndex - this.currentIndex);\n\t\t\t\t\tconst currentNode = this.itemNodeInfos[this.currentSwiperIndex];\n\t\t\t\t\tif (!!currentNode){\n\t\t\t\t\t\tthis.bottomDotXForIndex = this._getBottomDotX(currentNode, barWidth);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tconst currentIndex = this.currentSwiperIndex;\n\t\t\t\tlet nextIndex = currentIndex + (isRight ? 1 : -1);\n\t\t\t\tnextIndex = Math.max(0, nextIndex);\n\t\t\t\tnextIndex = Math.min(nextIndex, this.itemNodeInfos.length - 1);\n\t\t\t\tconst currentNodeInfo = this.itemNodeInfos[currentIndex];\n\t\t\t\tconst nextNodeInfo = this.itemNodeInfos[nextIndex];\n\t\t\t\tconst nextBottomX = nextNodeInfo ? this._getBottomDotX(nextNodeInfo, barWidth) : 0;\n\t\t\t\tif (isLineMode){\n\t\t\t\t\tthis.bottomDotX = this.bottomDotXForIndex + (nextBottomX - this.bottomDotXForIndex) * Math.abs(dxRate);\n\t\t\t\t} else if (isWormMode) {\n\t\t\t\t\tif ((isRight && currentIndex >= this.itemNodeInfos.length - 1) || (!isRight && currentIndex <= 0)) return;\n\t\t\t\t\tconst spaceOffset = isRight ? nextNodeInfo.right - currentNodeInfo.left : currentNodeInfo.right - nextNodeInfo.left;\n\t\t\t\t\tlet barCalcedWidth = barWidth + spaceOffset * Math.abs(dxRate);\n\t\t\t\t\tif (isRight) {\n\t\t\t\t\t\tif (barCalcedWidth > nextBottomX - this.bottomDotX + barWidth) {\n\t\t\t\t\t\t\tconst barMinusWidth = barWidth + spaceOffset * (1 - dxRate);\n\t\t\t\t\t\t\tthis.bottomDotX = this.bottomDotXForIndex + (barCalcedWidth - barMinusWidth) / 2;\n\t\t\t\t\t\t\tbarCalcedWidth = barMinusWidth;\n\t\t\t\t\t\t}\n\t\t\t\t\t}else if (!isRight) {\n\t\t\t\t\t\tif (barCalcedWidth > this.bottomDotXForIndex + barWidth - nextBottomX){\n\t\t\t\t\t\t\tconst barMinusWidth = barWidth + spaceOffset * (1 + dxRate);\n\t\t\t\t\t\t\tbarCalcedWidth = barMinusWidth;\n\t\t\t\t\t\t\tthis.bottomDotX = nextBottomX;\n\t\t\t\t\t\t} else{\n\t\t\t\t\t\t\tthis.bottomDotX = this.bottomDotXForIndex - (barCalcedWidth - barWidth);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tbarCalcedWidth = Math.max(barCalcedWidth, barWidth);\n\t\t\t\t\tthis.barCalcedWidth = barCalcedWidth;\n\t\t\t\t}\n\t\t\t},\n\t\t\t//在swiper的@animationfinish中通知z-tabs结束多setDx的锁定，若在父组件中调用了setDx，则必须调用unlockDx\n\t\t\tunlockDx() {\n\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\tthis.shouldSetDx = true;\n\t\t\t\t})\n\t\t\t},\n\t\t\t//更新z-tabs内部布局\n\t\t\tupdateSubviewLayout(tryCount = 0) {\n\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\tlet delayTime = 10;\n\t\t\t\t\t// #ifdef APP-NVUE || MP-BAIDU\n\t\t\t\t\tdelayTime = 50;\n\t\t\t\t\t// #endif\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tthis._getNodeClientRect('.z-tabs-scroll-view-conatiner').then(res=>{\n\t\t\t\t\t\t\tif (res){ \n\t\t\t\t\t\t\t\tif (!res[0].width && tryCount < 10) {\n\t\t\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\t\t\ttryCount ++;\n\t\t\t\t\t\t\t\t\t\tthis.updateSubviewLayout(tryCount);\n\t\t\t\t\t\t\t\t\t}, 50);\n\t\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tthis.tabsWidth = res[0].width;\n\t\t\t\t\t\t\t\tthis.tabsHeight = res[0].height;\n\t\t\t\t\t\t\t\tthis.tabsLeft = res[0].left;\n\t\t\t\t\t\t\t\tthis._handleListChange(this.list);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t\tthis._getNodeClientRect('.z-tabs-conatiner').then(res=>{\n\t\t\t\t\t\t\tif(res && res[0].width){\n\t\t\t\t\t\t\t\tthis.tabsSuperWidth = res[0].width;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t},delayTime)\n\t\t\t\t})\n\t\t\t},\n\t\t\t//点击了tabs\n\t\t\ttabsClick(index,item) {\n\t\t\t\tif (item.disabled) return;\n\t\t\t\tif (this.currentIndex != index) {\n\t\t\t\t\tthis.shouldSetDx = false;\n\t\t\t\t\tthis.$emit('change', index, item[this.valueKey]);\n\t\t\t\t\tthis.currentIndex = index;\n\t\t\t\t\tthis._preUpdateDotPosition(index);\n\t\t\t\t} else {\n\t\t\t\t\tthis.$emit('secondClick',index, item[this.valueKey]);\n\t\t\t\t}\n\t\t\t},\n\t\t\t//scroll-view滚动\n\t\t\tscroll(e){\n\t\t\t\tthis.currentScrollLeft = e.detail.scrollLeft;\n\t\t\t},\n\t\t\t//锁定dx，用于避免在swiper被动触发滚动时候执行setDx中的代码\n\t\t\t_lockDx() {\n\t\t\t\tthis.shouldSetDx = false;\n\t\t\t},\n\t\t\t//更新底部dot位置之前的预处理\n\t\t\t_preUpdateDotPosition(index) {\n\t\t\t\t// #ifndef APP-NVUE\n\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\tuni.createSelectorQuery().in(this).select(\".z-tabs-scroll-view\").fields({\n\t\t\t\t\t  scrollOffset: true\n\t\t\t\t\t}, data => {\n\t\t\t\t\t\tif (data) {\n\t\t\t\t\t\t\tthis.currentScrollLeft = data.scrollLeft;\n\t\t\t\t\t\t\tthis._updateDotPosition(index);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis._updateDotPosition(index);\n\t\t\t\t\t\t}\n\t\t\t\t\t}).exec();\n\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t\t\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\tthis._updateDotPosition(index);\n\t\t\t\t// #endif\n\t\t\t},\n\t\t\t//更新底部dot位置\n\t\t\t_updateDotPosition(index){\n\t\t\t\tif(index >= this.itemNodeInfos.length) return;\n\t\t\t\tthis.$nextTick(async ()=>{\n\t\t\t\t\tlet node = this.itemNodeInfos[index];\n\t\t\t\t\tlet offset = 0;\n\t\t\t\t\tlet tabsContainerWidth = this.tabsContainerWidth;\n\t\t\t\t\tif (JSON.stringify(this.activeStyle) !== '{}') {\n\t\t\t\t\t\tconst nodeRes = await this._getNodeClientRect(`#z-tabs-item-${index}`,true);\n\t\t\t\t\t\tif (nodeRes) {\n\t\t\t\t\t\t\tnode = nodeRes[0];\n\t\t\t\t\t\t\toffset = this.currentScrollLeft;\n\t\t\t\t\t\t\tthis.tabsHeight = Math.max(node.height + uni.upx2px(28), this.tabsHeight);\n\t\t\t\t\t\t\ttabsContainerWidth = 0;\n\t\t\t\t\t\t\tfor(let i = 0;i < this.itemNodeInfos.length;i++){\n\t\t\t\t\t\t\t\tlet oldNode = this.itemNodeInfos[i];\n\t\t\t\t\t\t\t\ttabsContainerWidth += i === index ? node.width : oldNode.width;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tthis.bottomDotX = this._getBottomDotX(node, this.finalBarWidth, offset);\n\t\t\t\t\tthis.bottomDotXForIndex = this.bottomDotX;\n\t\t\t\t\tif (this.tabsWidth) {\n\t\t\t\t\t\tsetTimeout(()=>{\n\t\t\t\t\t\t\tlet scrollLeft = this.bottomDotX - this.tabsWidth / 2 + this.finalBarWidth / 2;\n\t\t\t\t\t\t\tscrollLeft = Math.max(0,scrollLeft);\n\t\t\t\t\t\t\tif (tabsContainerWidth) {\n\t\t\t\t\t\t\t\tscrollLeft = Math.min(scrollLeft,tabsContainerWidth - this.tabsWidth + 10);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (this.shouldScroll && tabsContainerWidth > this.tabsWidth) {\n\t\t\t\t\t\t\t\tthis.scrollLeft = scrollLeft;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tthis.$nextTick(()=>{\n\t\t\t\t\t\t\t\tthis.isFirstLoaded = true;\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t},200)\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 处理list改变\n\t\t\t_handleListChange(newVal) {\n\t\t\t\tthis.$nextTick(async ()=>{\n\t\t\t\t\tif(newVal.length){\n\t\t\t\t\t\tlet itemNodeInfos = [];\n\t\t\t\t\t\tlet tabsContainerWidth = 0;\n\t\t\t\t\t\tlet delayTime = 0;\n\t\t\t\t\t\t// #ifdef MP-BAIDU\n\t\t\t\t\t\tdelayTime = 100;\n\t\t\t\t\t\t// #endif\n\t\t\t\t\t\tsetTimeout(async()=>{\n\t\t\t\t\t\t\tfor(let i = 0;i < newVal.length;i++){\n\t\t\t\t\t\t\t\tconst nodeRes = await this._getNodeClientRect(`#z-tabs-item-${i}`,true);\n\t\t\t\t\t\t\t\tif(nodeRes){\n\t\t\t\t\t\t\t\t\tconst node = nodeRes[0];\n\t\t\t\t\t\t\t\t\tnode.left += this.currentScrollLeft;\n\t\t\t\t\t\t\t\t\titemNodeInfos.push(node);\n\t\t\t\t\t\t\t\t\ttabsContainerWidth += node.width;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tif (i === this.currentIndex){\n\t\t\t\t\t\t\t\t\tthis.itemNodeInfos = itemNodeInfos;\n\t\t\t\t\t\t\t\t\tthis.tabsContainerWidth = tabsContainerWidth;\n\t\t\t\t\t\t\t\t\tthis._updateDotPosition(this.currentIndex);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tthis.itemNodeInfos = itemNodeInfos;\n\t\t\t\t\t\t\tthis.tabsContainerWidth = tabsContainerWidth;\n\t\t\t\t\t\t\tthis._updateDotPosition(this.currentIndex);\n\t\t\t\t\t\t},delayTime)\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t\t\n\t\t\t\tif (this.initTriggerChange && this.changeTriggerFailed && newVal.length) {\n\t\t\t\t\tif (this.current < newVal.length) {\n\t\t\t\t\t\tthis.$emit('change', this.current, newVal[this.current][this.valueKey]);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\t//根据node获取bottomX\n\t\t\t_getBottomDotX(node, barWidth = this.finalBarWidth, offset = 0){\n\t\t\t\treturn node.left + node.width / 2 - barWidth / 2 + offset - this.tabsLeft;\n\t\t\t},\n\t\t\t//获取节点信息\n\t\t\t_getNodeClientRect(select, withRefArr = false) {\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\tselect = select.replace('.', '').replace('#', '');\n\t\t\t\tconst ref = withRefArr ? this.$refs[select][0] : this.$refs[select];\n\t\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\t\tif (ref) {\n\t\t\t\t\t\tweexDom.getComponentRect(ref, option => {\n\t\t\t\t\t\t\tif (option && option.result) {\n\t\t\t\t\t\t\t\tresolve([option.size]);\n\t\t\t\t\t\t\t} else resolve(false);\n\t\t\t\t\t\t})\n\t\t\t\t\t} else resolve(false);\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t\t// #endif\n\t\t\t\tconst res = uni.createSelectorQuery().in(this);\n\t\t\t\tres.select(select).boundingClientRect();\n\t\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\t\tres.exec(data => {\n\t\t\t\t\t\tresolve((data && data != '' && data != undefined && data.length) ? data : false);\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t},\n\t\t\t//格式化badge中的count\n\t\t\t_formatCount(count) {\n\t\t\t\tif (!count) return '';\n\t\t\t\tif (count > this.badgeMaxCount) {\n\t\t\t\t\treturn this.badgeMaxCount + '+';\n\t\t\t\t}\n\t\t\t\treturn count.toString();\n\t\t\t},\n\t\t\t//将文本的px或者rpx转为px的值\n\t\t\t_convertTextToPx(text) {\n\t\t\t\tconst dataType = Object.prototype.toString.call(text);\n\t\t\t\tif (dataType === '[object Number]') {\n\t\t\t\t\treturn uni.upx2px(text);\n\t\t\t\t}\n\t\t\t\tlet isRpx = false;\n\t\t\t\tif (text.indexOf('rpx') !== -1 || text.indexOf('upx') !== -1) {\n\t\t\t\t\ttext = text.replace('rpx', '').replace('upx', '');\n\t\t\t\t\tisRpx = true;\n\t\t\t\t} else if (text.indexOf('px') !== -1) {\n\t\t\t\t\ttext = text.replace('px', '');\n\t\t\t\t} else {\n\t\t\t\t\ttext = uni.upx2px(text);\n\t\t\t\t}\n\t\t\t\tif (!isNaN(text)) {\n\t\t\t\t\tif (isRpx) return Number(uni.upx2px(text));\n\t\t\t\t\treturn Number(text);\n\t\t\t\t}\n\t\t\t\treturn 0;\n\t\t\t}\n\t\t}\n\t\t\n\t}\n</script>\n\n<style scoped>\n\t.z-tabs-conatiner{\n\t\t/* #ifndef APP-NVUE */\n\t\toverflow: hidden;\n\t\tdisplay: flex;\n\t\twidth: 100%;\n\t\t/* #endif */\n\t\t/* #ifdef APP-NVUE */\n\t\twidth: 750rpx;\n\t\t/* #endif */\n\t\tflex-direction: row;\n\t\theight: 80rpx;\n\t}\n\t\n\t.z-tabs-scroll-view-conatiner{\n\t\tflex: 1;\n\t\tposition: relative;\n\t\t/* #ifndef APP-NVUE */\n\t\tdisplay: flex;\n\t\theight: 100%;\n\t\twidth: 100%;\n\t\t/* #endif */\n\t\tflex-direction: row;\n\t}\n\t\n\t/* #ifndef APP-NVUE */\n\t.z-tabs-scroll-view ::-webkit-scrollbar {\n\t\tdisplay: none;\n\t\t-webkit-appearance: none;\n\t\twidth: 0 !important;\n\t\theight: 0 !important;\n\t\tbackground: transparent;\n\t}\n\t/* #endif */\n\t\n\t.z-tabs-scroll-view{\n\t\tflex-direction: row;\n\t\tposition: absolute;\n\t\tleft: 0;\n\t\ttop: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\t/* #ifndef APP-NVUE */\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\t/* #endif */\n\t\tflex: 1;\n\t}\n\t\n\t.z-tabs-list-container{\n\t\tposition: relative;\n\t\t/* #ifndef APP-NVUE */\n\t\theight: 100%;\n\t\t/* #endif */\n\t}\n\t\n\t.z-tabs-list,.z-tabs-list-container{\n\t\t/* #ifndef APP-NVUE */\n\t\tdisplay: flex;\n\t\t/* #endif */\n\t\tflex-direction: row;\n\t}\n\t\n\t.z-tabs-item{\n\t\t/* #ifndef APP-NVUE */\n\t\tdisplay: flex;\n\t\t/* #endif */\n\t\tflex-direction: row;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t\tpadding: 0px 20rpx;\n\t}\n\t\n\t.z-tabs-item-title-container{\n\t\t/* #ifndef APP-NVUE */\n\t\tdisplay: flex;\n\t\t/* #endif */\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t}\n\t\n\t.z-tabs-item-title{\n\t\tfont-size: 30rpx;\n\t}\n\t\n\t.z-tabs-item-title-disabled{\n\t\t/* #ifndef APP-NVUE */\n\t\tcursor: not-allowed;\n\t\t/* #endif */\n\t}\n\t\n\t.z-tabs-item-badge{\n\t\tmargin-left: 8rpx;\n\t\tbackground-color: #ec5b56;\n\t\tcolor: white;\n\t\tfont-size: 22rpx;\n\t\tborder-radius: 100px;\n\t\tpadding: 0rpx 10rpx;\n\t}\n\t\n\t.z-tabs-bottom{\n\t\tposition: absolute;\n\t\tbottom: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t}\n\t\n\t.z-tabs-bottom-dot{\n\t\tborder-radius: 100px;\n\t}\n\t\n\t.z-tabs-left,.z-tabs-right{\n\t\t/* #ifndef APP-NVUE */\n\t\tdisplay: flex;\n\t\t/* #endif */\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t}\n</style>\n\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./z-tabs.vue?vue&type=style&index=0&id=320c567f&scoped=true&lang=css&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./z-tabs.vue?vue&type=style&index=0&id=320c567f&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753690214554\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}