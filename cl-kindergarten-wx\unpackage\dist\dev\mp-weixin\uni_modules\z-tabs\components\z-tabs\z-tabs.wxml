<view class="z-tabs-conatiner data-v-320c567f" style="{{$root.s0}}"><view class="z-tabs-left data-v-320c567f"><slot name="left"></slot></view><view data-ref="z-tabs-scroll-view-conatiner" class="z-tabs-scroll-view-conatiner data-v-320c567f vue-ref"><scroll-view class="z-tabs-scroll-view data-v-320c567f vue-ref" scroll-x="{{true}}" scroll-left="{{scrollLeft}}" show-scrollbar="{{false}}" scroll-with-animation="{{isFirstLoaded}}" data-ref="z-tabs-scroll-view" data-event-opts="{{[['scroll',[['scroll',['$event']]]]]}}" bindscroll="__e"><view class="z-tabs-list-container data-v-320c567f" style="{{$root.s1}}"><view class="z-tabs-list data-v-320c567f" style="{{$root.s2}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="z-tabs-item data-v-320c567f vue-ref-in-for" style="{{$root.s3}}" id="{{'z-tabs-item-'+index}}" data-ref="{{'z-tabs-item-'+index}}" data-event-opts="{{[['tap',[['tabsClick',[index,'$0'],[[['list','',index]]]]]]]}}" bindtap="__e"><view class="z-tabs-item-title-container data-v-320c567f"><text class="{{['data-v-320c567f',(true)?'z-tabs-item-title':'',(item.$orig.disabled)?'z-tabs-item-title-disabled':'']}}" style="{{item.s4}}">{{''+(item.$orig[nameKey]||item.$orig)+''}}</text><block wx:if="{{item.g0}}"><text class="z-tabs-item-badge data-v-320c567f" style="{{item.s5}}">{{item.m0}}</text></block></view></view></block></view><view class="z-tabs-bottom data-v-320c567f" style="{{'width:'+(tabsContainerWidth+'px')+';'+('bottom:'+(finalBottomSpace+'px')+';')}}"><view data-ref="z-tabs-bottom-dot" class="z-tabs-bottom-dot data-v-320c567f vue-ref" style="{{$root.s6}}"></view></view></view></scroll-view></view><view class="z-tabs-right data-v-320c567f"><slot name="right"></slot></view></view>