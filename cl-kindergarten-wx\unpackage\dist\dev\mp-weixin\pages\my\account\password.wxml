<view class="content data-v-57a0e757"><u-form vue-id="c706f6e2-1" model="{{form}}" error-type="{{['toast']}}" data-ref="uForm" class="data-v-57a0e757 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><u-form-item vue-id="{{('c706f6e2-2')+','+('c706f6e2-1')}}" label="原密码:" labelWidth="120" prop="password" class="data-v-57a0e757" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('c706f6e2-3')+','+('c706f6e2-2')}}" placeholder="请输入原密码" value="{{form.password}}" data-event-opts="{{[['^input',[['__set_model',['$0','password','$event',[]],['form']]]]]}}" class="data-v-57a0e757" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('c706f6e2-4')+','+('c706f6e2-1')}}" label="新密码:" labelWidth="120" prop="news_password" class="data-v-57a0e757" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('c706f6e2-5')+','+('c706f6e2-4')}}" placeholder="请输入要修改的新密码" value="{{form.news_password}}" data-event-opts="{{[['^input',[['__set_model',['$0','news_password','$event',[]],['form']]]]]}}" class="data-v-57a0e757" bind:__l="__l"></u-input></u-form-item><u-button vue-id="{{('c706f6e2-6')+','+('c706f6e2-1')}}" type="primary" data-event-opts="{{[['^click',[['submit']]]]}}" bind:click="__e" class="data-v-57a0e757" bind:__l="__l" vue-slots="{{['default']}}">确定</u-button></u-form></view>