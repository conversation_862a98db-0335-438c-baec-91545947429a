@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.u-cell.data-v-57e16312 {
  display: flex;
  flex-direction: row;
  align-items: center;
  position: relative;
  box-sizing: border-box;
  width: 100%;
  padding: 26rpx 32rpx;
  font-size: 28rpx;
  line-height: 54rpx;
  color: #606266;
  background-color: #fff;
  text-align: left;
}
.u-cell_title.data-v-57e16312 {
  font-size: 28rpx;
}
.u-cell__left-icon-wrap.data-v-57e16312 {
  margin-right: 10rpx;
  font-size: 32rpx;
}
.u-cell__right-icon-wrap.data-v-57e16312 {
  margin-left: 10rpx;
  color: #969799;
  font-size: 28rpx;
}
.u-cell__left-icon-wrap.data-v-57e16312,
.u-cell__right-icon-wrap.data-v-57e16312 {
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 48rpx;
}
.u-cell-border.data-v-57e16312:after {
  position: absolute;
  box-sizing: border-box;
  content: " ";
  pointer-events: none;
  border-bottom: 1px solid #e4e7ed;
  right: 0;
  left: 0;
  top: 0;
  -webkit-transform: scaleY(0.5);
          transform: scaleY(0.5);
}
.u-cell-border.data-v-57e16312 {
  position: relative;
}
.u-cell__label.data-v-57e16312 {
  margin-top: 6rpx;
  font-size: 26rpx;
  line-height: 36rpx;
  color: #909399;
  word-wrap: break-word;
}
.u-cell__value.data-v-57e16312 {
  overflow: hidden;
  text-align: right;
  vertical-align: middle;
  color: #909399;
  font-size: 26rpx;
}
.u-cell__title.data-v-57e16312,
.u-cell__value.data-v-57e16312 {
  flex: 1;
}
.u-cell--required.data-v-57e16312 {
  overflow: visible;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.u-cell--required.data-v-57e16312:before {
  position: absolute;
  content: "*";
  left: 8px;
  margin-top: 4rpx;
  font-size: 14px;
  color: #fa3534;
}
.u-cell_right.data-v-57e16312 {
  line-height: 1;
}
