{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/pages/my/index.vue?888b", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/pages/my/index.vue?960b", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/pages/my/index.vue?c665", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/pages/my/index.vue?d9b7", "uni-app:///pages/my/index.vue", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/pages/my/index.vue?4e7c", "webpack:///E:/IdeaProject/kindergarten_accounting/cl-kindergarten-wx/pages/my/index.vue?11b8"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "baseInfo", "name", "phone", "avatar", "company_name", "registerDate", "login_code", "auth", "version", "onLoad", "uni", "success", "console", "that", "onShow", "onPullDownRefresh", "methods", "upload_avatar", "count", "sizeType", "sourceType", "url", "filePath", "formData", "floder", "_this", "getUserInfo", "service_center", "profile", "account", "setting", "about"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACqK;AACrK,gBAAgB,+KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uPAEN;AACP,KAAK;AACL;AACA,aAAa,qOAEN;AACP,KAAK;AACL;AACA,aAAa,+QAEN;AACP,KAAK;AACL;AACA,aAAa,yQAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChDA;AAAA;AAAA;AAAA;AAA+lB,CAAgB,ynBAAG,EAAC,C;;;;;;;;;;;;;;;;;;ACmCnnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAC;IACA;MAEAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;IACA;EACA;EACAC;IAEA;IACA;IACAC;MACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IAAA;IACAJ;MACA;MACAA;IACA;EACA;EAEA;EACAK;IACA;IACA;IACAL;EACA;;EAGAM;IAEAC;MACA;MACAP;QACAQ;QACAC;QAAA;QACAC;QAAA;QACAT;UAEAD;YACAW;YACAC;YACArB;YACAsB;cACAC;YACA;YACAb;cACA;;cAEA;cACAC;cACA;gBACAT;cACA;cACAsB;gBACA;kBACAA;gBACA;kBACA;gBACA;cACA;YAEA;UACA;QAIA;MACA;IACA;IACAC;MAAA;MACA;QACA;QACA;QACA;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChJA;AAAA;AAAA;AAAA;AAAkqC,CAAgB,woCAAG,EAAC,C;;;;;;;;;;;ACAtrC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/my/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/my/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=4dcceeb0&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=4dcceeb0&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4dcceeb0\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/my/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=4dcceeb0&scoped=true&\"", "var components\ntry {\n  components = {\n    uAvatar: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-avatar/u-avatar\" */ \"@/uni_modules/uview-ui/components/u-avatar/u-avatar.vue\"\n      )\n    },\n    uTag: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-tag/u-tag\" */ \"@/uni_modules/uview-ui/components/u-tag/u-tag.vue\"\n      )\n    },\n    uCellGroup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-cell-group/u-cell-group\" */ \"@/uni_modules/uview-ui/components/u-cell-group/u-cell-group.vue\"\n      )\n    },\n    uCellItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-cell-item/u-cell-item\" */ \"@/uni_modules/uview-ui/components/u-cell-item/u-cell-item.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"content\">\r\n\t  <view class=\"head\">\r\n\t\t  <u-avatar :src=\"baseInfo.avatar\" size=\"140\" mode=\"circle\" class=\"avatar\" :show-level=\"true\" level-icon =\"camera\" @click=\"upload_avatar\"></u-avatar>\r\n\t\t  <view class=\"personInfo\">\r\n\t\t\t  <view>{{baseInfo.name}}</view>\r\n\t\t\t  <view class=\"account\">帐号：{{baseInfo.phone}}</view>\r\n\t\t\t  \r\n\t\t  </view>\t\r\n\t\t  <view class=\"auth\">\r\n\t\t\t  <u-tag text=\"已实名\" shape=\"circleLeft\" type=\"success\"  v-if=\"baseInfo.auth==1\"/>\r\n\t\t\t  <u-tag text=\"未实名\"  shape=\"circleLeft\" type=\"error\"  v-if=\"baseInfo.auth==0\"/>\r\n\t\t  </view>\r\n\t  </view>\r\n\t  \r\n\t<u-cell-group>\r\n\t\t\t<u-cell-item icon=\"account-fill\" title=\"个人信息\" @click=\"profile\"></u-cell-item>\r\n\t\t\t<u-cell-item icon=\"lock-fill\" title=\"帐号&安全\" @click=\"account\"></u-cell-item>\r\n\t\t\t\r\n\t\t\t<u-cell-item icon=\"setting-fill\" title=\"设置\" @click=\"setting\"></u-cell-item>\r\n\t\t\t<u-cell-item icon=\"server-fill\" title=\"服务中心\" @click=\"service_center\"></u-cell-item>\r\n\t  \t\t\r\n\t\t\t<u-cell-item icon=\"info-circle\" title=\"关于我们\" @click=\"about\"></u-cell-item>\r\n\t</u-cell-group>\r\n\t\r\n\t\r\n\t\r\n\t\r\n\t\r\n\t\t\r\n\t\t\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n\timport {toast, clearStorageSync, setStorageSync, getStorageSync, useRouter} from '@/utils/utils.js'\r\n  export default {\r\n    data(){\r\n      return {\r\n\t\t  \r\n\t\t  baseInfo: {\r\n\t\t\t  name:'',\r\n\t\t\t  phone:'',\r\n\t\t\t  avatar:'',\r\n\t\t\t  company_name:'',\r\n\t\t\t  registerDate:'',\r\n\t\t\t  login_code:'',\r\n\t\t\t  auth: 0,\r\n\t\t  },\r\n\t\t  version:\"\",\r\n      }\r\n    },\r\n\tonLoad() {\r\n\t\t\r\n\t\tthis.getUserInfo()\r\n\t\tconst that = this\r\n\t\tuni.getSystemInfo({\r\n\t\t\tsuccess: function (res) {\r\n\t\t\t\tconsole.log(\"res\", res)\r\n\t\t\t\tthat.version = res.appVersion\r\n\t\t\t}\r\n\t\t})\r\n\t}, \r\n\tonShow() {\r\n\t\tuni.$on('refresh', e => {\r\n\t\t    this.getUserInfo()\r\n\t\t    uni.$off('refresh') \r\n\t\t})\r\n\t},\r\n\t\r\n\t// 下拉刷新\r\n\tonPullDownRefresh() {\r\n\t\t//console.log('refresh');\r\n\t\tthis.getUserInfo()\r\n\t\tuni.stopPullDownRefresh();//停止刷新\r\n\t},\r\n\t\r\n\t\r\n    methods: {\r\n\t\t\r\n\t\tupload_avatar(){\r\n\t\t\tconst _this=this\r\n\t\t\tuni.chooseImage({\r\n\t\t\t\tcount: 1, \r\n\t\t\t\tsizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有\r\n\t\t\t\tsourceType: ['album','camera'], //从相册选择\r\n\t\t\t\tsuccess: function (res) {\r\n\r\n\t\t\t\t\tuni.uploadFile({\r\n\t\t\t\t\t\turl: _this.$api_url + '/api/index/upload_cos', \r\n\t\t\t\t\t\tfilePath: res.tempFilePaths[0],\r\n\t\t\t\t\t\tname: 'file',\r\n\t\t\t\t\t\tformData: {\r\n\t\t\t\t\t\t\tfloder: 'avatar'\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tsuccess: (uploadFileRes) => {\r\n\t\t\t\t\t\t\t//let retData = JSON.parse(uploadFileRes)\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tlet retData = JSON.parse(uploadFileRes.data)\r\n\t\t\t\t\t\t\tconsole.log('up:',retData)\r\n\t\t\t\t\t\t\tconst _data ={\r\n\t\t\t\t\t\t\t\tavatar: retData.data.file\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t_this.$api.baseInfoSave(_data).then(ret => {\r\n\t\t\t\t\t\t\t\tif (ret.code == 1) {\r\n\t\t\t\t\t\t\t\t\t_this.baseInfo.avatar = retData.data.file\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\ttoast(res.msg)\t\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}) \r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t\t\r\n\t\t\t\t\t\r\n\t\t\t\t\t\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tgetUserInfo() {\r\n\t\t\tthis.$api.baseInfo().then(res => {\r\n\t\t\t\t//console.log(res)\r\n\t\t\t\tthis.baseInfo = res.data\r\n\t\t\t\tthis.avatar_src= res.data.avatar\r\n\t\t\t})\r\n\t\t},\r\n\t\t\r\n\t\tservice_center(){\r\n\t\t\ttoast('未设置跳转，请自行定义')\r\n\t\t},\r\n\t\t\r\n\t\tprofile(){\r\n\t\t\tuseRouter('/pages/my/profile',{} ,'navigateTo')\r\n\t\t},\r\n\t\taccount(){\r\n\t\t\tuseRouter('/pages/my/account/index',{} ,'navigateTo')\r\n\t\t},\r\n\t\tsetting(){\r\n\t\t\tuseRouter('/pages/my/account/setting',{} ,'navigateTo')\r\n\t\t},\r\n\t\tabout(){\r\n\t\t\tuseRouter('/pages/my/about/index',{} ,'navigateTo')\r\n\t\t},\r\n    }\r\n  }\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n .content{\r\n\t \r\n\t .head{\r\n\t\t background-color:rgb(71, 144, 255);\r\n\t\t display: flex;\r\n\t\t \r\n\t\t min-height: 300rpx;\r\n\t\t padding-top: 60rpx;\r\n\t\t .avatar{\r\n\t\t\t margin-left: 50rpx;\r\n\t\t }\r\n\t\t .personInfo{\r\n\t\t\t color:#fff;\r\n\t\t\t margin-top: 25rpx;\r\n\t\t\t font-size: 30rpx;\r\n\t\t\t margin-left: 30rpx;\r\n\t\t\t line-height: 50rpx;\r\n\t\t\t .account{font-size: 26rpx;}\r\n\t\t }\r\n\t\t .auth{flex: 1;text-align: right;margin-top: 40rpx;}\r\n\t\t\r\n\t}\r\n }\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=4dcceeb0&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=4dcceeb0&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753690216555\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}