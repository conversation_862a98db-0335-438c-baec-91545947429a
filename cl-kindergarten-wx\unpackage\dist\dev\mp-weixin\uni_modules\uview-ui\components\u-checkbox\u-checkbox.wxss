@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.u-checkbox.data-v-c4a74aee {
  display: inline-flex;
  align-items: center;
  overflow: hidden;
  -webkit-user-select: none;
          user-select: none;
  line-height: 1.8;
}
.u-checkbox__icon-wrap.data-v-c4a74aee {
  color: #606266;
  flex: none;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  width: 42rpx;
  height: 42rpx;
  color: transparent;
  text-align: center;
  transition-property: color, border-color, background-color;
  font-size: 20px;
  border: 1px solid #c8c9cc;
  transition-duration: 0.2s;
}
.u-checkbox__icon-wrap--circle.data-v-c4a74aee {
  border-radius: 100%;
}
.u-checkbox__icon-wrap--square.data-v-c4a74aee {
  border-radius: 6rpx;
}
.u-checkbox__icon-wrap--checked.data-v-c4a74aee {
  color: #fff;
  background-color: #2979ff;
  border-color: #2979ff;
}
.u-checkbox__icon-wrap--disabled.data-v-c4a74aee {
  background-color: #ebedf0;
  border-color: #c8c9cc;
}
.u-checkbox__icon-wrap--disabled--checked.data-v-c4a74aee {
  color: #c8c9cc !important;
}
.u-checkbox__label.data-v-c4a74aee {
  word-wrap: break-word;
  margin-left: 10rpx;
  margin-right: 24rpx;
  color: #606266;
  font-size: 30rpx;
}
.u-checkbox__label--disabled.data-v-c4a74aee {
  color: #c8c9cc;
}
