<view class="content"><u-form class="vue-ref" vue-id="5daef304-1" model="{{form}}" error-type="{{['toast']}}" data-ref="uForm" bind:__l="__l" vue-slots="{{['default']}}"><u-form-item vue-id="{{('5daef304-2')+','+('5daef304-1')}}" label="姓名:" label-width="170" prop="cardno" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('5daef304-3')+','+('5daef304-2')}}" placeholder="请输入姓名" value="{{form.name}}" data-event-opts="{{[['^input',[['__set_model',['$0','name','$event',[]],['form']]]]]}}" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('5daef304-4')+','+('5daef304-1')}}" label="身份证号:" label-width="170" prop="cardno" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('5daef304-5')+','+('5daef304-4')}}" placeholder="请输入身份证号" value="{{form.cardno}}" data-event-opts="{{[['^input',[['__set_model',['$0','cardno','$event',[]],['form']]]]]}}" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('5daef304-6')+','+('5daef304-1')}}" label="身份证正反面:" label-width="200" bind:__l="__l" vue-slots="{{['default']}}"><u-upload class="vue-ref" vue-id="{{('5daef304-7')+','+('5daef304-6')}}" upload-text="选择或拍照" width="180" height="180" action="{{action}}" fileList="{{fileList}}" form-data="{{form_data}}" data-ref="uUpload" bind:__l="__l"></u-upload></u-form-item><view class="submit_con"><u-button vue-id="{{('5daef304-8')+','+('5daef304-1')}}" type="primary" data-event-opts="{{[['^click',[['submit']]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}">提交</u-button></view></u-form></view>